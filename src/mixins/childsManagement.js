import moment from "moment";

export default {
	methods: {
		fillFormWithChildData(inputs, data, isReception = false) {
			inputs.forEach(
				function (input) {
					if (data.validated && !isReception) {
						input.disabled = true;
					}
					if (!(input.not_fill_on_reception === "true" && this.isReceptionMode)) {
						if (input.name === "name" && data?.name) {
							input.value = data?.name;
						}
						if (input.name === "surname" && data?.surname) {
							input.value = data?.surname;
						}
						if (input.name === "birthday" && data?.birthday_date) {
							input.value = data?.birthday_date;
						}
						if (input.name === "address" && data?.address?.street) {
							input.value = data.address.street;
						}
						if (input.name === "postal_code" && data?.address?.postal_code) {
							input.value = data.address.postal_code;
						}
						if (input.name === "municipality" && data?.address?.city) {
							input.value = data.address.city;
						}
						if (input.name === "email" && data?.email) {
							input.value = data.email;
						}
						if (input.name === "telephone" && data?.telephone) {
							if (typeof data.telephone === 'object') {
								input.value = `${data.telephone.value}`;
								input.countryCode = data.telephone.countryCode;
							} else {
								input.value = `${data.telephone}`;
							}
						}
						if (input.options) {
							input.options.forEach(
								function (option) {
									if (
										input.name === "nationality" &&
										data?.nationality &&
										option.value === data.nationality
									) {
										input.value = option.value;
									} else if (
										(input.name === "kinship" &&
											data?.kinship &&
											option === data.kinship)
									) {
										input.value = option || "son";
									}
								}.bind(this),
							);
						}
					}
					
				}.bind(this),
			);

			return inputs;
		},
		childRequiresDocument(birthday, checkInDate, maxAgeToNotRequireID) {
			const ageAtCheckinDate = moment(checkInDate).diff(
				moment(birthday),
				"years",
			);
			return !(ageAtCheckinDate <= maxAgeToNotRequireID);
		},
	},
};
