import Index from "@/views/Index.vue";
import AppLayout from "@/components/layouts/AppLayout";
import ErrorPage from "@/views/Error";
import store from "@/store/index";
import { calculateStep } from "./helpers";

export const receptionRoutes = [
	{
		path: "",
		redirect: "/login",
		name: "ReceptionIndex",
		component: AppLayout,
		children: [
			{
				path: "/login",
				component: () =>
					import(/* webpackChunkName: "login" */ "../../views/Login.vue"),
				name: "<PERSON><PERSON>",
			},
		],
	},
	{
		path: "/:brandId(\\d+)",
		component: AppLayout,
		name: "ReceptionMode",
		meta: {
			totalSteps: () =>
				calculateStep("totalSteps", "reception", {
					signedDocuments: store.state.brand.config.signed_documents,
				}),
		},
		children: [
			{
				path: "",
				component: Index,
				name: "Index",
			},
			{
				path: "/:brandId(\\d+)",
				name: "BrandSelector",
				component: () =>
					import(
						/* webpackChunkName: "brandSelector" */ "../../views/BrandSelector.vue"
					),
				meta: {
					step: () => 1,
					stepName: "brandSelector",
				},
			},
			{
				path: "search",
				name: "Search",
				component: () =>
					import(/* webpackChunkName: "search" */ "../../views/Search.vue"),
				meta: {
					step: () => 2,
					stepName: "reservation",
				},
			},
			{
				path: "reservations",
				name: "Reservations",
				component: () =>
					import(
						/* webpackChunkName: "reservations" */ "../../views/Reservations.vue"
					),
				meta: {
					step: () => 2,
					stepName: "reservation",
					allowBack: true,
					backRoute: "Search",
				},
			},
			{
				path: "status",
				name: "Status",
				component: () =>
					import(/* webpackChunkName: "status" */ "../../views/Status.vue"),
				meta: {
					step: () => 2,
					stepName: "reservation",
					allowBack: true,
					backRoute: "Reservations",
				},
			},
			{
				path: "privacy",
				name: "Privacy",
				component: () =>
					import(/* webpackChunkName: "privacy" */ "../../views/Gdpr.vue"),
				meta: {
					step: () => 3,
					stepName: "privacy",
					allowBack: true,
					backRoute: "Status",
				},
			},
			{
				path: "scan",
				name: "Scan",
				component: () =>
					import(/* webpackChunkName: "scan" */ "../../views/Scan.vue"),
				meta: {
					step: () => 4,
					stepName: "scan",
						active: () => {
						return store.state.guest.list.some(guest => !guest.date_of_issue);
					},
					redirect: "Privacy",
				},
			},
			{
				path: "advanced-scan",
				name: "AdvancedScan",
				component: () =>
					import(
						/* webpackChunkName: "advancedScan" */ "../../views/AdvancedScan.vue"
					),
				meta: {
					step: () => 4,
					stepName: "scan",
					active: () => {
						return store.state.guest.list.some(guest => !guest.date_of_issue);
					},
					redirect: "Privacy",
				},
			},
			{
				path: "sensible-data",
				name: "SensibleData",
				component: () =>
					import(
						/* webpackChunkName: "advancedScan" */ "../../views/SensibleDataForm.vue"
					),
				meta: {
					step: () => 4,
					stepName: "scan",
				},
			},
			{
				path: "validate-data",
				name: "ValidateData",
				component: () =>
					import(
						/* webpackChunkName: "validateData" */ "../../views/ValidateData.vue"
					),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "reception", {
							scanOnReception: store.state.guest.list.some(guest => !guest.date_of_issue),
						});
					},
					stepName: "identityVerification",
				},
			},
			{
				path: "childs-data",
				name: "ChildsData",
				component: () =>
					import(
						/* webpackChunkName: "childsData" */ "../../views/ChildsData.vue"
					),
				meta: {
					step: () => 5,
					stepName: "identityVerification",
					active: () => {
						return (
							store.state.brand.config.child_data_with_holder && 
							store.state.brand.config.children_process_on_reception &&
							store.getters["guest/getSelectedGuest"]?.holder &&
							store.getters["guest/getChildGuests"]?.length
						);
					},
					redirect: "Documents",
				},
			},
			{
				path: "child-form",
				name: "ChildForm",
				component: () =>
					import(
						/* webpackChunkName: "childform" */ "../../views/ChildForm.vue"
					),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "reception", {
							scanOnReception: store.state.guest.list.some(guest => !guest.date_of_issue),
						});
					},
					stepName: "identityVerification",
					allowBack: true,
					backRoute: "Status",
				},
			},
			{
				path: "documents",
				name: "Documents",
				component: () =>
					import(
						/* webpackChunkName: "documents" */ "../../views/Documents.vue"
					),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "reception", {
							scanOnReception: store.state.guest.list.some(guest => !guest.date_of_issue),
						});
					},
					stepName: "documents",
					active: () => {
						return store.state.brand.config.signed_documents;
					},
					redirect: "Confirmation",
					allowBack: true,
					backRoute: "ValidateData",
				},
			},
			{
				path: "signature",
				name: "Signature",
				component: () =>
					import(
						/* webpackChunkName: "signature" */ "../../views/Signature.vue"
					),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "reception", {
							scanOnReception: store.state.guest.list.some(guest => !guest.date_of_issue),
						});
					},
					stepName: "documents",
					allowBack: true,
					backRoute: "Documents",
					active: () => {
						return store.state.brand.config.signed_documents;
					},
					redirect: "Confirmation",
				},
			},
			{
				path: "send-documents",
				name: "SendDocuments",
				component: () =>
					import(
						/* webpackChunkName: "sendDocuments" */ "../../views/SendDocuments.vue"
					),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "reception", {
							scanOnReception: store.state.guest.list.some(guest => !guest.date_of_issue),
						});
					},
					stepName: "documents",
					active: () => {
						return store.state.brand.config.signed_documents;
					},
					redirect: "Confirmation",
				},
			},
			{
				path: "confirmation",
				name: "Confirmation",
				component: () =>
					import(
						/* webpackChunkName: "confirmation" */ "../../views/Confirmation.vue"
					),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "reception", {
							signedDocuments: store.state.brand.config.signed_documents,
							scanOnReception: store.state.guest.list.some(guest => !guest.date_of_issue),
						});
					},
					stepName: "confirmation",
				},
			},
			{
				path: "share",
				name: "Share",
				component: () =>
					import(/* webpackChunkName: "share" */ "../../views/Share.vue"),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "reception", {
							signedDocuments: store.state.brand.config.signed_documents,
							scanOnReception: store.state.guest.list.some(guest => !guest.date_of_issue),
						});
					},
					stepName: "confirmation",
				},
			},
			{
				path: "error",
				name: "Error",
				component: ErrorPage,
			},
		],
	},
	{
		path: "*",
		name: "error404",
		component: ErrorPage,
	},
];
