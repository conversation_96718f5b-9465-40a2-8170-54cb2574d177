import Vue from "vue";
import store from "@/store/index";
import VueRouter from "vue-router";
import clearAllStore from "@/utils/clearAllStore";
import { autocheckinRoutes } from "./routes/autocheckin";
import { receptionRoutes } from "./routes/reception";
import { Auth } from "@aws-amplify/auth";
import receptionUtils from "../utils/receptionUtils";

const REGEX_CHUNK = /chunk [\s\S]+ failed/i;

Vue.use(VueRouter);

const routes = receptionUtils.setReceptionMode()
	? receptionRoutes
	: autocheckinRoutes;

const router = new VueRouter({
	mode: "history",
	base: process.env.BASE_URL,
	routes,
});

router.afterEach((to,from) => {
	window.scrollTo(0,0);
	const fullName =
		store.getters["guest/getSelectedGuest"]?.full_name || "not_set";
	console.info(`User ${fullName} arrive to ${to.name} page`);
});

window.popStateDetected = false;
window.allowGoBack = false;
window.addEventListener("popstate", () => {
	window.popStateDetected = true;
});

router.beforeEach(async function (to, from, next) {
	const brandIdStore = store.state.brand.brandId;
	const brandIdRoute = to.params.brandId;

	if (to.query.uuid) {
		store.dispatch("trace/SET_BOUNCER_UUID", to.query.uuid);
	}

	store.dispatch("queryParams/SET_DATA", { ...from.query, ...to.query });

	const backButtonPressed = window.popStateDetected;
	const allowBack = window.allowGoBack;
	window.popStateDetected = false;
	window.allowGoBack = false;
	if (backButtonPressed && !allowBack) {
		next(false);
		return;
	}

	/*
    Check if page is error, then do nothing.
     */
	if (to.name === "Error" || to.name === "error404") {
		next();
		return;
	}

	/*
    Check if brand in URL is the same as brand in store
     */
	console.debug(
		"check brand URL and storage are equal",
		parseInt(brandIdRoute),
		brandIdStore,
	);
	if (
		to.name !== "Index" &&
		parseInt(brandIdStore) !== parseInt(brandIdRoute) &&
		!store.state.app.isReceptionMode
	) {
		await clearStore();
		next({ path: `/${brandIdRoute ? brandIdRoute : brandIdStore}` });
		return;
	}

	// Check that reception is logged in
	if (
		store.state.app.isReceptionMode &&
		!(await Auth.currentUserInfo()) &&
		to.name !== "Login" &&
		from.name !== "Login"
	) {
		console.info("Redirecting to login page cause user is not authenticated");
		next({ path: "/login" });
		return;
	}

	// Check that reception is authorized to access brand
	if (
		store.state.app.isReceptionMode &&
		to.name !== "Login" &&
		!store.state.app.authorizedBrands.includes(brandIdRoute)
	) {
		console.info(
			"Redirecting to first authenticated brand cause user try to login to another brand",
		);
		next({ path: `/${store.state.app.authorizedBrands[0]}` });
		return;
	}

	/*
  Check if brand id and store id are 0, then send to error if both are 0
   */
	console.debug(
		"Check if brand id in url and in store are present",
		parseInt(brandIdRoute),
		brandIdStore,
	);
	if (
		to.name !== "Error" &&
		brandIdStore === 0 &&
		parseInt(brandIdRoute) === 0 &&
		!store.state.app.isReceptionMode
	) {
		next({ name: "Error", params: { error: "error404" } });
		return;
	}

	// If GDPR is not signed, redirect to gdpr page
	if (
		!store.state.app.isReceptionMode &&
		!store.state.gdpr.accepted &&
		!["Index", "BrandSelector", "Privacy"].includes(to.name)
	) {
		console.debug("Gdpr not accepted, redirecting to gdpr page");
		next({ name: "Privacy", params: { brandId: brandIdRoute } });
		return;
	}

	/*
  Check if checkin is completed, send to Confirmation
   */
	console.debug(
		"Checkin is completed and the current view is not 'Share'",
		store.state.reservations.reservationCheckinComplete,
		to.name !== "Share",
	);
	if (
		store.state.reservations.reservationCheckinComplete &&
		to.name !== "Share" &&
		to.name !== "Confirmation"
	) {
		next({ name: "Confirmation", params: { brandId: brandIdRoute } });
		return;
	}

	/*
    Check if route is deactivated and go to next route if so
   */
	if (to.meta.active && !to.meta.active()) {
		console.debug("redirecting from deactivated route");
		next({ name: to.meta.redirect });
		return;
	}

	if (brandIdStore !== brandIdRoute) {
		await store.dispatch("brand/SET_BRAND_ID", brandIdRoute);
	}

	console.debug(`All ok, next moving to: ${to.name}`);
	next();
});

/*
 * Reset vuex to defaults
 */
async function clearStore() {
	console.debug("clearStore");
	clearAllStore(store);
}

router.onError((error) => {
	if (REGEX_CHUNK.test(error.message)) {
		console.error("Detected chunk failed, the page will be reloaded");
		if (store.state.retryCount.retryCount <= 3) {
			store.dispatch("retryCount/ADD_TRY");
			window.location.reload();
			return;
		}
	}
	store.dispatch("retryCount/CLEAR_TRY");
});

export default router;
