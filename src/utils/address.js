import{ postcodeValidator,postcodeValidatorExistsForCountry} from "postcode-validator"
// Validación Address para residentes de Irlanda
export function getSuggestionFilters(selectedCountry) {
  if (selectedCountry === "IRL") {
    return { countries: ["IRL", "GBR"] };
  }
  return { countries: [selectedCountry] };
}

//Validación PostalCode para residentes de Irlanda
export function isIreland(code) {
  return code === "IE" || code === "IRL";
}

export function shouldValidateIEorUK(country) {
  return isIreland(country);
}

export function validateIEorUKPostalCode(postalCode) {
  return ( postcodeValidator(postalCode, "IE") || postcodeValidator(postalCode, "GB"));
}

export function validatePostalCodeForCountry(postalCode, country) {
  if (postcodeValidatorExistsForCountry(country)) {
    return postcodeValidator(postalCode, country);
  }
  return true;
}