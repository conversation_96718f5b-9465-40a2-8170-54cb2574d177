import Guest from "@/entities/Guest.js";
import { getMostSimilarGuest } from "@/utils/guestSimilarity";

const getDefaultState = () => {
	return {
		list: [],
		emailValidationAttempt: 0,
	};
};

const normalizeIdentityDocumentsSaved = (documents) => {
	if (!Array.isArray(documents)) {
		// If 'documents' is not an array, return a default object with empty properties
		return {
			reservationId: "",
			brandId: "",
			bucket: "",
			files: [],
		};
	}

	return documents.reduce(
		(normalized, identityDocument) => {
			/* Restructured back to the way it used to save the files in identityDocumentsSaved
			 	This is done because now we send 2 petitions when there is more than 1 identityDocument being scanned,
			 	and so identityDocumentsSaved turned into an array, hence why we are turning it back into an object. 
			 */
			normalized.reservationId = identityDocument.reservationId;
			normalized.brandId = identityDocument.brandId;
			normalized.bucket = identityDocument.bucket;

			const fileObject = {
				name: identityDocument.files[0].fileName,
				key: identityDocument.key, // Now the 'key' is nested inside 'files'
				url: identityDocument.files[0].url,
			};
			normalized.files.push(fileObject);

			return normalized;
		},
		{
			reservationId: "",
			brandId: "",
			bucket: "",
			files: [],
		},
	);
};

export default {
	namespaced: true,
	state: getDefaultState(),

	getters: {
		getGuestsCheckinComplete: (state) =>
			state.list.every((guest) => guest.validated),

		getFirstGuest: (state) =>
			state.list.find(
				(element) => element.pax_type === "AD" && element.validated,
			),

		getSelectedGuest: (state) => {
			return state.list.find((guest) => guest.selected) ?? {};
		},

		getLiteSelectedGuestData: (state, getters) => {
			const guest = getters.getSelectedGuest;
			const guestProperties = [
				"name",
				"surname",
				"second_surname",
				"gender",
				"birthday_date",
				"email",
				"pax_type",
				"pms_id",
				"kinship",
				"nationality",
				"residence_country",
				"document_type",
				"document_number",
				"document_support_number",
				"date_of_issue",
				"date_of_expiry",
			];

			return Object.assign(
				{},
				...guestProperties.map((key) => ({ [key]: guest[key] })),
			);
		},

		getGuestIndex: (state) => (searchedGuest) => {
			return (
				state.list.findIndex((guest) => guest?.uuid === searchedGuest?.uuid) ??
				null
			);
		},

		getCompletedGuests: (state) => {
			return state.list.filter((guest) => guest.processCompleted === true);
		},
		getGuestsWithSignedDocuments: (state) => {
			return state.list.filter((guest) => guest.signedDocuments === true);
		},
		isHolderGuestCompleted: (state, getters) => {
			return getters.getCompletedGuests.some((guest) => guest.holder === true);
		},
		getHolderGuest: (state) => {
			return state.list.find((guest) => guest.holder === true) ?? state.list[0];
		},
		getChildGuests: (state) => {
			return state.list.filter(
				(guest) => guest.pax_type === "BB" || guest.pax_type === "CH",
			);
		},
		getGuestsToSynchronize: (state) => {
			return state.list.filter(
				(guest) => guest?.mustBeSynchronized || guest.selected,
			);
		},
	},

	actions: {
		UPDATE_GUEST: (context, data) => {
			context.commit("updateGuest", data);
		},
		UPDATE_DOCUMENTS_EMAIL: (context, email) => {
			context.commit("updateDocumentsEmail", email);
		},
		MERGE_GUEST_SCAN_DATA: (context, data) => {
			const holderNotModifiable = context.rootState.brand.config.reservation_holder_not_modifiable;
			context.commit("mergeGuestScanData", {data, holderNotModifiable});
		},
		ADD_EMAIL_VALIDATION_ATTEMPT: (context) => {
			context.commit("addEmailValidatorAttempt");
		},
		CLEAR_DATA: (context) => {
			context.commit("clearData");
		},
		SET_GUEST_TO_LIST: (context, guests) => {
			const partialCheckin = context.rootState.brand.config.partial_checkin;
			context.commit("setGuestToList", guests, partialCheckin);
		},
		CLEAR_STATE: (context) => {
			context.commit("clearState");
		},
		SELECT_GUEST: (context, uuid) => {
			context.commit("selectGuest", uuid);
		},
		SIMILAR_GUESTS: ({ state, getters }, guestData) => {
			const currentGuestList = state.list.filter((guest) => !guest.selected);
			const selectedGuest = getters.getSelectedGuest;

			return getMostSimilarGuest(guestData, currentGuestList, selectedGuest);
		},
		CLEAR_SELECTED_GUESTS: (context) => {
			context.commit("clearSelectedGuests");
		},
		RESET_SELECTED_GUEST: (context, reservationGuests) => {
			context.commit("resetSelectedGuest", reservationGuests);
		},
	},

	mutations: {
		updateGuest: (state, data) => {
			if (data.email) {
				alert("updateGuest - sobrescribiendo email con: " + data.email);
			}
			state.list = state.list.map((guest) => {
				if (guest.selected) {
					if (data.identityDocumentsSaved) {
						// Normalize identityDocumentsSaved to handle the new data structure
						data.identityDocumentsSaved = normalizeIdentityDocumentsSaved(
							data.identityDocumentsSaved,
						);
					}
					return new Guest({ ...guest, ...data });
				}
				return guest;
			});
		},

		updateDocumentsEmail: (state, email) => {
			console.log("🔍 DEBUG updateDocumentsEmail mutation:");
			console.log("  - email recibido:", email);

			state.list = state.list.map((guest) => {
				if (guest.selected) {
					console.log("  - guest antes:", { email: guest.email, documentsEmail: guest.documentsEmail });
					const updatedGuest = new Guest({ ...guest, documentsEmail: email });
					console.log("  - guest después:", { email: updatedGuest.email, documentsEmail: updatedGuest.documentsEmail });
					return updatedGuest;
				}
				return guest;
			});
		},

		mergeGuestScanData: (state, { data, holderNotModifiable }) => {
			if (data.email) {
				alert("mergeGuestScanData - intentando mergear email: " + data.email);
			}
			const scanKeys = Object.keys(data);
			const scanDataGuest = new Guest(data);
			state.list.forEach((guest) => {
				if (guest.selected) {
					Object.keys(scanDataGuest).forEach((dataScanKey) => {
						const dataScanReceived = scanDataGuest[dataScanKey];
						const dataSavedInStore = guest[dataScanKey];
						if (dataSavedInStore && typeof dataSavedInStore === "object") {
							Object.keys(dataSavedInStore).forEach((key) => {
								if (dataScanReceived) {
									if (dataScanReceived[key]) {
										guest[dataScanKey][key] = dataScanReceived[key];
									}
								}
							});
						}
						// If the field is the telephone, email or name with the configuration of not modifying the holder we will keep the PMS data.
						if (dataScanKey !== "email" && dataScanKey !== "telephone" && !((dataScanKey === "name" || dataScanKey === "surname") && guest.holder && holderNotModifiable)) {
							if (dataScanReceived) {
								guest[dataScanKey] = dataScanReceived;
							} else if (
								(dataScanReceived === null || dataScanReceived === "") &&
								scanKeys.includes(dataScanKey)
							) {
								guest[dataScanKey] = "";
							}
						}
					});
				}
				return guest;
			});
		},

		selectGuest: (state, uuid) => {
			state.list.forEach((item) => {
				item.selected = item.uuid === uuid;
				return item;
			});
		},

		clearSelectedGuests: (state) => {
			state.list = state.list.map((guest) => ({
				...guest,
				selected: false,
			}));
		},

		addEmailValidatorAttempt: (state) => {
			state.emailValidationAttempt += 1;
		},

		clearData: (state) => {
			state.list = [];
		},

		clearState: (state) => {
			Object.assign(state, getDefaultState());
		},

		setGuestToList: (state, guests, partialCheckin) => {
			state.list = guests.map((guest) => {
				if (!partialCheckin) {
					guest.processCompleted = guest.validated;
				}
				return new Guest(guest);
			});
		},

		resetSelectedGuest: (state, reservationGuests) => {
			state.list.forEach((guest, key) => {
				if (guest.selected && reservationGuests && reservationGuests[key]) {
					Object.assign(guest, reservationGuests[key]);
					guest.selected = true;
				}
			});
		},
	},
};
