import { countries } from "@/utils/countries";
import { ccaaList, provinces } from "@/utils/ccaa";
import { americanCountries } from "@/utils/americanCountries.js";

const getDefaultState = () => {
	return {
		brandId: 0,
		name: "",
		country: "",
		logo: "",
		mainColor: "",
		backgroundImage_lg: "",
		backgroundImage_md: "",
		backgroundImage_sm: "",
		config: {},
		children: [],
		products: [],
	};
};

export default {
	namespaced: true,
	state: getDefaultState(),
	getters: {
		getConfig(state) {
			return state.config;
		},
		mainColor(state) {
			return state.mainColor;
		},
		getActiveProducts(state) {
			const activeProducts = state.products.filter((product) => {
				return product.active;
			});
			return activeProducts.map((item) => item.name);
		},
		getAutocheckinActive(state) {
			const brands = state.children.length ? state.children : [{ ...state }];
			return brands.filter((child) => {
				return child.products?.some((product) => {
					return product.name === "autocheckin" && product.active;
				});
			});
		},
		langs: (state) => [
			{
				iso2: "es",
				flag: "es",
			},
			{
				iso2: "en",
				flag: americanCountries.includes(state.country) ? "us" : "gb",
			},
			{
				iso2: "de",
				flag: "de",
			},
			{
				iso2: "pt",
				flag: "pt",
			},
			{
				iso2: "fr",
				flag: "fr",
			},
			{
				iso2: "it",
				flag: "it",
			},
			{
				iso2: "ru",
				flag: "ru",
			},
		],
	},
	actions: {
		SET_BRAND_ID: (context, brandID) => {
			context.commit("setBrandID", brandID);
		},
		SET_BRAND: (context, brand) => {
			context.commit("setBrand", brand);
		},
		SET_CONFIG: (context, config) => {
			context.commit("setConfig", config);
		},
		SET_MAIN_COLOR: (context, color) => {
			context.commit("setMainColor", color);
		},
		SET_BRAND_LOGO: (context, logo) => {
			context.commit("setBrandLogo", logo);
		},
		SET_BRAND_NAME: (context, name) => {
			context.commit("setBrandName", name);
		},
	},
	mutations: {
		setBrandID: (state, brandID) => {
			state.brandId = brandID;
		},
		setBrand: (state, brand) => {
			state.children = brand.children || [];

			state.brandId = brand.id;
			state.name = brand.name;
			state.logo = brand.logo;

			state.mainColor = brand.background_color
				? brand.background_color
				: state.children[0]?.background_color ||
				  process.env.VUE_APP_MAIN_COLOR_FALLBACK;

			if (!state.logo || !state.mainColor || !state.name) {
				console.info("Brand info missing", { state, brand });
			}
			// TODO: remove default value when all hotels have place_country in database
			state.country = brand.place_country || "ES";
			state.timeZone = brand.time_zone || "Europe/Madrid";

			const urlBg = brand.background_image
				? brand.background_image
				: state.children[0]?.background_image || null;
			state.products = brand.products || [];
			state.backgroundImage_lg = urlBg?.replace("original", "large");
			state.backgroundImage_md = urlBg?.replace("original", "medium");
			state.backgroundImage_sm = urlBg?.replace("original", "small");
			state.products = brand.products;
		},
		setConfig: (state, config) => {
			config.max_attempts_validate_telephone_code = 3;
			//Add email validation
			config.max_attempts_email_validation = 3;
			// Filter active inputs
			config.identification.validate_data_scan = [
				config.identification.validate_data_scan[0].filter(
					(input) => input.active === "true",
				),
			];

			// Add country options to inputs that have countryInput attribute
			Object.entries(config.identification).forEach(([, array]) => {
				array.forEach((nestedItem) => {
					Object.entries(nestedItem).forEach(([, item]) => {
						if (item.countryInput) {
							item.options = countries;
						}
						if (item.name === "CCAA") {
							item.options = [...ccaaList];
						}

						if (item.name === "province") {
							item.options = [...provinces];
						}
					});
				});
			});

			console.log("🚀 Brand setConfig ejecutándose");
			console.log("🔍 separate_documents_email en config:", config.separate_documents_email);
			alert("Brand setConfig - separate_documents_email: " + config.separate_documents_email);
			state.config = config;
		},
		setMainColor: (state, color) => {
			state.mainColor = color;
		},
		setBrandLogo: (state, logo) => {
			state.logo = logo;
		},
		setBrandName: (state, name) => {
			state.name = name;
		},
		clearState: (state) => {
			Object.assign(state, getDefaultState());
		},
	},
};
