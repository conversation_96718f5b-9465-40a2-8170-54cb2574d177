<template>
  <div class="scan main-content main-content-white">
    <modal name="documentModal" @closeModal="modalClosed">
      <img class="m-auto" :src="this.documentImage" />
    </modal>
    <modal @closeModal="modalClosed" name="signatureRequired">
      <div>
        <p>{{ $t("scan.modalSignatureRequiredText", { hotelName: this.brandName}) }}</p>
      </div>
    </modal>
    <modal
      name="requiredDataModal"
      :button="false"
      data-test="sensible-data-modal"
    >
      <p class="mb-4">
        {{ $t("scan.needExtraInfoBody") }}
      </p>
      <form-input
        v-if="imageOcr && !imageOcr.document_type"
        @inputChanged="documentTypeChanged($event)"
        class="mb-6"
        :active="true"
        :optional="false"
        :name="$t('validatedata.document_type')"
        :type="'select'"
        :options="getTranslatedDocumentTypeOptions"
        :inputName="'document_type'"
        placeholder="..."
        :check-on-start="false"
        data-test="document-type-sensible-input"
      />
      <form-input
        v-if="imageOcr && !imageOcr.nationality && (imageOcr.document_type === 'identity_card' || documentTypeValue === 'identity_card'|| imageOcr.document_type === 'driving_license'|| documentTypeValue === 'driving_license')"
        @autocompleteSelect="autocompleteSelect($event, 'Nationality')"
        @inputChanged="nationalityChanged($event, 'Nationality')"
        class="mb-6"
        :active="true"
        :optional="false"
        :name="$t('validatedata.nationality')"
        :type="'autocomplete'"
        :options="nationalitySuggestion"
        :inputName="'nationality'"
        :value="nationalityValue"
        placeholder="..."
        :check-on-start="false"
        data-test="nationality-sensible-input"
      />
      <form-input
        v-if="imageOcr && !imageOcr.issuing_country && (imageOcr.document_type === 'residence_permit' || documentTypeValue === 'residence_permit')"
        @autocompleteSelect="autocompleteSelect($event, 'IssuingCountry')"
        @inputChanged="nationalityChanged($event, 'IssuingCountry')"
        class="mb-6"
        :active="true"
        :optional="false"
        :name="$t('validatedata.issuing_country')"
        :type="'autocomplete'"
        :options="nationalitySuggestion"
        :inputName="'issuingCountry'"
        :value="issuingCountryValue"
        placeholder="..."
        :check-on-start="false"
        data-test="issuing-country-sensible-input"
      />
      <btn
        :disabled="isContinueButtonDisabled"
        data-test="sensible-modal-button"
        @click.native="completeSensibleData()"
      >
        {{ $t("validatedata.button") }}
      </btn>
    </modal>
    <modal name="scanInfo" :button="false">
      <p v-html="$t('scan.modalOptionalMessage')" />
      <btn class="my-4" @click.native="VISIBLE(false)">{{
        $t("scan.modalOptionalScanNowButton")
      }}</btn>
      <btn data-test="goToCompleteDataManually" @click.native="handleManualProcess">{{
        $t("scan.modalOptionalNotScanButton")
      }}</btn>
    </modal>
    <modal name="tips" :button="false" class="tips">
      <div>
        <ul>
          <li class="flex flex-row items-start mb-4">
            <camera-shake class="scan-modal-icon" />
            <span class="ml-4 text-sm">{{ $t("scan.dontShake") }}</span>
          </li>
          <li class="flex flex-row mb-4">
            <camera-composition class="scan-modal-icon" />
            <span class="ml-4 text-sm">{{ $t("scan.goodFraming") }}</span>
          </li>
          <li class="flex flex-row">
            <camera-flash class="scan-modal-icon" />
            <span class="ml-4 text-sm">{{ $t("scan.dontFlash") }}</span>
          </li>
        </ul>
      </div>
      <div class="flex justify-center">
        <btn
          data-test="closeModal"
          class="button"
          @click.native="uploadImageEvent"
          >{{ $t("modal.button") }}</btn
        >
      </div>
    </modal>
    <modal @closeModal="modalClosed" name="errorModal">
      <p>
        {{ messageError }}
      </p>
    </modal>
    <modal name="similarGuest" :button="false">
      <p
        class="text-gray-500 text-justify"
        v-if="similarGuest && similarGuest.processCompleted"
      >
        {{ $t("scan.error.similarGuestError") }}
      </p>
      <div v-if="similarGuest" class="flex flex-col mb-4">
        <span v-if="similarGuest.full_name"
          >{{ $t("validatedata.name") }}: {{ similarGuest.full_name }}</span
        >
        <span v-if="similarGuest.document_number"
          >{{ $t("validatedata.document_number") }}:
          {{ similarGuest.document_number }}
        </span>
        <span v-if="similarGuest.birthday_date"
          >{{ $t("validatedata.birthday_date") }}:
          {{ similarGuest.birthday_date }}</span
        >
      </div>
      <p>{{ $t("scan.error.similarGuestConfirm") }}</p>
      <btn
        class="bg-white hover:bg-gray-50 border my-4"
        textColor="black"
        @click.native="handleSelectedGuest(guestData)"
        data-test="different-guest-btn"
      >
        {{ $t("shared.no") }}
      </btn>
      <btn
        @click.native="handleSimilarGuestInReservation"
        data-test="same-guest-btn"
      >
        {{ $t("shared.yes") }}
      </btn>
    </modal>
    <div class="content h-full flex flex-col justify-between">
      <section class="flex flex-col">
        <title-component>{{ title }}</title-component>
        <p class="text-gray-800 mb-4">
          {{ subtitle }}
        </p>
        <input
          data-test="fileInput"
          type="file"
          @input="onFileChanged"
          ref="fileUpload"
          hidden
          accept="image/png, image/jpeg, image/jpg"
        />
        <documentUploader
          data-test="fileButton"
          type="front"
          :uploadedDocument="
            getIdentityDocumentByName('identity_card_front') ||
              getIdentityDocumentByName('passport') ||
                getIdentityDocumentByName('driving_license') || getIdentityDocumentByName('residence_permit_front')
          "
          :allRequiredDocuments="getRequiredDocuments(config)"
          :scannedWithSignature="scannedWithSignature"
          :backPartScanned="backPartScanned"
          @handleClickEvent="uploadImageEvent"
          @handleShowDocumentEvent="handleShowDocumentEvent"
          @handleDeleteDocumentEvent="handleDeleteDocumentEvent"
        ></documentUploader>
        <documentUploader
          v-if="isDNI"
          data-test="fileButton"
          class="mt-2"
          type="back"
          :uploadedDocument="getIdentityDocumentByName('identity_card_back')"
          :allRequiredDocuments="getRequiredDocuments(config)"
          :backPartScanned="backPartScanned"
          @handleClickEvent="uploadImageEvent"
          @handleShowDocumentEvent="handleShowDocumentEvent"
          @handleDeleteDocumentEvent="handleDeleteDocumentEvent"
        ></documentUploader>
        <documentUploader
          v-if="isResidencePermit"
          data-test="fileButton"
          class="mt-2"
          type="back"
          :uploadedDocument="getIdentityDocumentByName('residence_permit_back')"
          :allRequiredDocuments="getRequiredDocuments(config)"
          :backPartScanned="backPartScanned"
          @handleClickEvent="uploadImageEvent"
          @handleShowDocumentEvent="handleShowDocumentEvent"
          @handleDeleteDocumentEvent="handleDeleteDocumentEvent"
        ></documentUploader>
        <documentUploader
          v-if="signatureUploadRequired"
          data-test="fileButton"
          class="mt-2"
          type="signature"
          :uploadedDocument="getIdentityDocumentByName('signature')"
          :allRequiredDocuments="getRequiredDocuments(config)"
          @handleClickEvent="uploadImageEvent"
          @handleShowDocumentEvent="handleShowDocumentEvent"
          @handleDeleteDocumentEvent="handleDeleteDocumentEvent"
        ></documentUploader>

        <div class="flex gap-4 mt-2" v-if="!getAllDocuments.length">
          <div class="w-10 flex">
            <info />
          </div>
          <span class="my-auto">{{ $t("scan.scanInfo") }}</span>
        </div>
        <p class="text-sm mb-6 mt-2" v-html="legalText"></p>
      </section>
      <section class="flex flex-col">
        <btn
          v-if="this.config.optional_scan"
          data-test="manualProcess-button"
          :disabled="allRequiredDocuments"
          class="mb-6"
          @click.native="handleManualProcess"
          >{{ $t("scan.addDataManually") }}</btn
        >
        <btn
          :disabled="!allRequiredDocuments"
          data-test="continue-button"
          @click.native="handleGoNext"
          >{{ $t("shared.continue") }}</btn
        >
        <button data-test="go-back" class="go-back-button" @click="goBack">
          {{ $t("shared.previousStep") }}
        </button>
      </section>
    </div>
  </div>
</template>
<script>
import modal from "@/components/shared/modal.component";
import titleComponent from "@/components/shared/title.component";
import documentUploader from "@/components/scan/documentUploader.component";
import startLoading from "@/mixins/startLoading";
import stopLoading from "@/mixins/stopLoading";
import redirect from "@/mixins/redirect";
import cameraShake from "@/assets/images/icons/camera-shake.svg";
import cameraFlash from "@/assets/images/icons/camera-flash.svg";
import info from "@/assets/images/icons/informacion.svg";
import cameraComposition from "@/assets/images/icons/camera-composition.svg";
import formInput from "@/components/search/formInput.component";
import validateInputs from "@/mixins/validateInputs";
import scan from "@/mixins/scan";
import sensibleData from "../mixins/sensibleData";
import { mapGetters, mapActions, mapState } from "vuex";
import repository from "../repository/repositoryFactory";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import btn from "@/components/shared/button.component";
import { Tracker } from "@/tracker";
const AutoCheckinApi = repository.get("checkin");

export default {
  name: "scan",
  data() {
    return {
      modalShown: false,
      manualProcessModalShown: false,
      hasError: false,
      messageError: null,
      selectedFile: null,
      modalMessage: null,
      documentValuesRequired: ["name", "surname", "birthday_date"],
      legalText: this.$t("scan.legalText"),
      legalTextType: 1,
      base64Image: null,
      imageOcr: null,
      imageType: null,
      similarGuest: {},
      documentImage: "",
      documentSide: "",
      imageProcessed: this.$route?.params.imageProcessed || false
    };
  },
  components: {
    titleComponent,
    modal,
    cameraShake,
    cameraFlash,
    info,
    cameraComposition,
    documentUploader,
    formInput,
    btn
  },
  mixins: [
    startLoading,
    stopLoading,
    browserHistoryManagement,
    validateInputs,
    redirect,
    scan,
    sensibleData
  ],
  watch: {
    "$i18n.locale": async function() {
      await this.LOADING(true);

      await this.getCustomLegalText();

      await this.LOADING(false);
    },
    allRequiredDocuments(newVal) {
      if (newVal && this.imageProcessed) {
        this.handleGoNext()
      }
    },
  },

  async created() {
    await this.LOADING(true);
    await this.CLEAR_MODAL_STATE();
    await this.getCustomLegalText();
    await this.RESET_SELECTED_GUEST(this.reservationSelected?.guests);
    await this.REMOVE_QUERY_PARAM("manualProcess");
    await this.stopLoading();
    Tracker.recordCustomEvent("guest_scan_start", {
      guest: this.guestLiteData
    });
    //in case its redirected from advancedScan view 
    if(this.allRequiredDocuments && this.imageProcessed){
      this.handleGoNext()
    }
  },
  computed: {
    title() {
    return this.config.identity_document_signature_required
      ? this.$t("scan.titleWithSignature")
      : this.$t("scan.title");
    },
    subtitle() {
    const baseSubtitle = this.$t("scan.text");
    const subtitleWithSignature = this.$t("scan.textWithSignature", {
      hotelName: this.brandName,
    });

    return this.config.identity_document_signature_required
      ? `${baseSubtitle} ${subtitleWithSignature}`
      : baseSubtitle;
  },
    ...mapState("scan", {
      currentAttemptsDocumentScan: "currentAttemptsDocumentScan",
      scannedWithSignature: "scannedWithSignature",
      backPartScanned: "backPartScanned",
      signatureUploadRequired: "signatureUploadRequired"
    }),
    ...mapState("modal", {
      modalName: "name",
    }),
    ...mapGetters("app", ["isReceptionMode", "isDemoMode"]),
    ...mapState("reservations", ["reservationSelected"]),
    ...mapGetters("guest", { guestData: "getSelectedGuest", guestLiteData: "getLiteSelectedGuestData" }),
    ...mapGetters("scan", [
      "getIdentityDocumentByName",
      "getRequiredDocuments",
      "isDNI",
      "isResidencePermit",
      "getScanData",
      "getAllDocuments"
    ]),
    ...mapState("guest", {
      guestList: "list"
    }),
    ...mapState("loading", ["loading"]),
    ...mapState("brand", {
      brandId: "brandId",
      brandName: "name",
      logo: "logo",
      mainColor: "mainColor",
      country: "country",
      config: "config"
    }),

    showPaxInfo() {
      let pax_type = this.guestData?.pax_type;

      if (pax_type !== "AD") {
        return [
          this.$t(`statusComponent.${pax_type}`).toLowerCase(),
          this.$t("scan.older")
        ];
      }

      return [
        this.$t("statusComponent.AD").toLowerCase(),
        this.$t("scan.younger")
      ];
    },
    //enables continue button and redirect directly to validateData view
    allRequiredDocuments() {
      return this.getRequiredDocuments(this.config);
    }
  },

  methods: {
    ...mapActions("scan", [
      "CLEAR_SCAN_DOCUMENT_ATTEMPT",
      "CLEAR_STATE",
      "ADD_IDENTITY_DOCUMENT",
      "CLEAR_DOCUMENT",
      "SET_SCANNED_WITH_SIGNATURE",
      "SET_BACK_PART_SCANNED",
      "SET_SIGNATURE_UPLOAD_REQUIRED"
    ]),
    ...mapActions("modal", {
      VISIBLE: "VISIBLE",
      SET_TITLE: "SET_TITLE",
      SET_TYPE: "SET_TYPE",
      SET_BUTTON_MESSAGE: "SET_BUTTON_MESSAGE",
      SET_NAME: "SET_NAME",
      CLEAR_MODAL_STATE: "CLEAR_STATE"
    }),
    ...mapActions("queryParams", {
      SET_QUERY_PARAM: "SET_DATA",
      REMOVE_QUERY_PARAM: "REMOVE_QUERY_PARAM"
    }),
    ...mapActions("guest", [
      "UPDATE_GUEST",
      "MERGE_GUEST_SCAN_DATA",
      "RESET_SELECTED_GUEST",
    ]),
    ...mapActions("loading", ["LOADING"]),
    ...mapGetters("reservations", ["getValidatedGuests"]),

    async getCustomLegalText() {
      if (this.config.custom_scan_text) {
        this.legalText = await AutoCheckinApi.getCustomizedText(
          this.brandId,
          this.legalTextType,
          this.$i18n.locale
        )
          .then(response => response.data[0].translations[0]?.text || null)
          .catch(async error => {
            console.error("Error getting scan legal text", { error });
            return null;
          });
      } else {
        this.legalText = this.$t("scan.legalText")
      }
    },

    async handleShowDocumentEvent(document) {
      this.documentImage = document.image;
      await this.CLEAR_MODAL_STATE();
      await this.SET_TYPE("info");
      await this.SET_BUTTON_MESSAGE(
        this.$t("searchFormComponent.reservation_code.modal.button")
      );
      await this.SET_NAME("documentModal");
      await this.VISIBLE(true);
    },

    async handleGoNext() {
      await this.LOADING(true);
      const frontDoc = this.getIdentityDocumentByName("identity_card_front");
      const isRomanianID = frontDoc && (frontDoc.data?.nationality === "ROU" || frontDoc.nationality === "ROU");
      
      if(this.imageProcessed && isRomanianID){
        this.imageProcessed = false;
        await this.LOADING(false);
        return;
      }
      // Merge documents data with guest
        await this.MERGE_GUEST_SCAN_DATA(this.getScanData(this.config.advanced_scan));
      // Upload identityDocuments to api
      if (
        this.config.send_identity_documents_to_PMS ||
        this.config.send_identity_documents_to_reception
      ) {
        let identityDocumentsSaved = await this.saveIdentityDocuments();
        if (identityDocumentsSaved) {
          this.UPDATE_GUEST({
            identityDocumentsSaved: identityDocumentsSaved
          });
          this.CLEAR_SCAN_DOCUMENT_ATTEMPT();
        } else {
          return this.redirect({
            name: "Error",
            params: { error: "scanError" }
          });
        }
      }
      this.CLEAR_ALL_DOCUMENTS();

      await this.sendGuestScanCompletedEvent(false);
      return await this.redirect({
        name: "ValidateData"
      });
    },

    async handleManualProcess(){
      if (this.config.optional_scan && !this.getRequiredDocuments(this.config)) {
        if (!this.manualProcessModalShown) {
          await this.showManualCompleteModal();
          this.manualProcessModalShown = true;
          return;
        }
        await this.SET_QUERY_PARAM({ manualProcess: true });
        await this.VISIBLE(false);
        await this.LOADING(true);
        await this.sendGuestScanCompletedEvent(true);
        return await this.redirect({
          name: "ValidateData"
        });
      }
    },

    async sendGuestScanCompletedEvent(manual) {
      Tracker.recordCustomEvent("guest_scan_completed", {
        manual,
        guest: this.guestLiteData
      });
    },

    handleDeleteDocumentEvent({ documentType }) {
      this.CLEAR_DOCUMENT(documentType);
    },
    /**
     * Enable input file event to show files modal
     */
    async uploadImageEvent(payload) {
      // When opening modal, payload changes to PointerEvent
      if (payload?.side) {
        this.documentSide = payload.side;
      }
      if (!this.modalShown && !this.error) {
        await this.openInfoModal();
      } else {
        if (!navigator.mediaDevices || !this.config.advanced_scan) {
          this.modalShown && (await this.VISIBLE(false));
          this.$refs.fileUpload.click();
        } else {
          return this.$router.push({
              name: 'AdvancedScan', 
              query: { side: this.documentSide }
          });
        }
        
      }
    },
    async openInfoModal() {
      await this.SET_TYPE("info");
      await this.SET_NAME("tips");
      await this.SET_TITLE(this.$t("scan.modalTipsTitle"));
      await this.VISIBLE(true);
      this.modalShown = true;
    },

    async startImageProcessing(image) {
      await this.LOADING(true);
      this.imageProcessed = true
      this.CLEAR_MODAL_STATE();
      this.base64Image = await this.toBase64(image);
      this.imageOcr = await this.sendImage([this.base64Image], this.documentSide);
      this.imageType = image?.type;

      if (this.imageOcr) {
        const isRejected = await this.rejectInvalidPassport(this.imageOcr);
        if (isRejected) {
          this.stopLoading();
          return;
        }
        /*
          We show a modal asking for extra information about the scanned document in case the document type could
          not be retrieved, or if we know it is a DNI/driving license without nationality, or if we know it is a residence permit without issuing country.
          This ensures that the back of the document is scanned if it is a DNI/residence permit and then we can reject the document if a DNI from outside the Schengen zone is entered or if the driving license/residence permit is from other country than the hotel.
        */
        if (
          !this.imageOcr.document_type && this.documentSide !== "signature" ||
          ((this.imageOcr.document_type === "identity_card" || this.imageOcr.document_type === "driving_license") &&
            !this.imageOcr.nationality && this.documentSide !== "signature") || (this.imageOcr.document_type === "residence_permit" && !this.imageOcr.issuing_country && this.documentSide !== "signature")
        ) {
          console.info("Sensible data modal shown", {
            ocrData: this.imageOcr
          });
          await this.SET_TYPE("info");
          await this.SET_NAME("requiredDataModal");
          await this.SET_TITLE(this.$t("scan.needExtraInfoTitle"));
          await this.VISIBLE(true);
        } else {
          if(this.documentSide === "signature"){
            this.imageOcr.document_type = "signature"
          }
          await this.validateData(this.imageOcr, {
            base64ImageSource: this.base64Image,
            imageType: this.imageType
          });
       
          if(this.config.identity_document_signature_required){
            await this.handleSignatureRequired();        
            if(this.modalShown && this.modalName === "signatureRequired") return;
          }  
        }
      }
     
      if(!this.allRequiredDocuments){
        this.stopLoading(); 
      }
    },

    async completeSensibleData() {
      if (await this.rejectInvalidPassport(this.imageOcr)) return;
      const isValidSensibleData = await this.checkSensibleData();
      if(this.documentSide === "signature"){
        this.imageOcr.document_type = "signature"
      }
      if (isValidSensibleData) {
        await this.validateData(this.imageOcr, {
          base64ImageSource: this.base64Image,
          imageType: this.imageType
        });
        if(this.config.identity_document_signature_required){
          await this.handleSignatureRequired();        
        } 
        if(!this.allRequiredDocuments){
          this.LOADING(false); 
       }
      }
    },

    /**
     * Send identity images to Autocheckin Api
     */
    async saveIdentityDocuments() {
      const requestBody = {
        brand_name: this.isDemoMode ? `${this.brandName}-demo` : this.brandName,
        brand_logo: this.logo,
        background_color: this.mainColor,
        guest_name: `${this.guestData.name} ${this.guestData.surname}`.trim(),
        reservation_id: this.reservationSelected.res_id,
        signature: null,
        documents: this.getAllDocuments
      };

      const promises = requestBody.documents.map((doc) => 
        AutoCheckinApi.createDocuments(this.brandId, {...requestBody, documents: [doc]})
      )
      const settledPromises = await Promise.allSettled(promises);
      const failedPromises = settledPromises
      .filter((result) => result.status === 'rejected');

      if (failedPromises.length > 0) {
        console.error('Some promises failed:', {failedPromises});
      }

      const successfulPromises = settledPromises
        .filter((result) => result.status === 'fulfilled')
        .map((result) => result.value.data);

      return successfulPromises
    },

    async showManualCompleteModal() {
      await this.SET_TYPE("info");
      await this.SET_NAME("scanInfo");
      await this.SET_TITLE(this.$t("scan.modalOptionalTitle"));
      await this.VISIBLE(true);
    }
  }
};
</script>
<style lang="scss" scoped>
.scan-modal-icon {
  min-width: 30px;
}
.info {
  width: 40px;
}
.button {
  @apply font-black py-2 px-4 rounded mt-8 text-white uppercase;
  background-color: var(--bgColor);
  width: auto;
  &:hover {
    background-color: var(--darkenBgColor);
  }
}
</style>
