<template>
  <div
    class="validate-data main-content main-content-white"
    v-show="rendered"
    :class="{ 'opacity-0': isLoading }"
  >
    <modal name="emailValidation">{{
      $t("senddocuments.emailValidationFailed")
    }}</modal>
    <modal :button="false" name="differences">
      {{ $t("validatedata.modalDuplicates") }}
      <ul class="list-disc ml-4 mt-6">
        <li
          v-for="(input, index) in valuesWithMuchDifference"
          :key="index"
          class="mb-4"
        >
          <p class="font-bold">{{ $t(`validatedata.${input[0].name}`) }}:</p>
          <span>
            {{ formatInputValue(input[0]) }} &#8594;
            {{ formatInputValue(input[1]) }}
          </span>
        </li>
      </ul>
      <div class="btn-wrapper flex mt-8 justify-between">
        <btn @click.native="closeModal" data-test="close-differences-modal">
          {{ $t("shared.no") }}
        </btn>
        <btn
          class="bg-white hover:bg-gray-50 border"
          textColor="black"
          @click.native="redirectNextStep"
          data-test="accept-differences-modal"
        >
          {{ $t("shared.yes") }}
        </btn>
      </div>
    </modal>

    <modal @closeModal="modalClosed" name="errorModal">
      <p>
        {{ messageError }}
      </p>
    </modal>

    <modal name="similarGuest" :button="false">
      <p
        class="text-gray-500 text-justify"
        v-if="similarGuest && similarGuest.processCompleted"
      >
        {{ $t("scan.error.similarGuestError") }}
      </p>
      <hr
        v-if="similarGuest && similarGuest.processCompleted"
        class="mt-2 mb-2 border-gray-300 border-t-2"
      />
      <div v-if="similarGuest" class="flex flex-col">
        <span v-if="similarGuest.full_name"
          >{{ $t("validatedata.name") }}: {{ similarGuest.full_name }}</span
        >
        <span v-if="similarGuest.document_number"
          >{{ $t("validatedata.document_number") }}:
          {{ similarGuest.document_number }}
        </span>
        <span v-if="similarGuest.birthday_date"
          >{{ $t("validatedata.birthday_date") }}:
          {{ similarGuest.birthday_date }}</span
        >
      </div>
      <hr class="mt-2 mb-2 border-gray-300 border-t-2" />
      <p>{{ $t("scan.error.similarGuestConfirm") }}</p>
      <div class="btn-wrapper flex mt-6 justify-between">
        <btn
          class="bg-white hover:bg-gray-50 border"
          textColor="black"
          @click.native="handleSelectedGuest(guestData)"
          data-test="different-guest-btn"
        >
          {{ $t("shared.no") }}
        </btn>
        <btn
          @click.native="handleSimilarGuestInReservation"
          data-test="same-guest-btn"
        >
          {{ $t("shared.yes") }}
        </btn>
      </div>
    </modal>
    <div class="content h-full flex flex-col">
      <title-component>
        {{ manualOrScan }}
      </title-component>
      <p class="text-gray-800 mb-4">
        {{ $t("validatedata.truthfulData") }}
      </p>
      <validate-data-card
        id="validateDataCard"
        v-if="showReceptionCard && isReceptionMode"
        :inputs="this.originalInputDataList"
        data-test="validate-data-card"
        @cardToggle="showAllInputs()"
      ></validate-data-card>
      <!-- eslint-disable-->

      <div
        v-for="input in inputDataList"
        :class="{ 'md:flex flex-wrap': input.name === 'address' }"
      >
        <form-input
          v-if="input.name === 'address'"
          :ref="'street_number'"
          :key="`street_number_${computedStreetNumber}`"
          class="mb-6 md:w-2/5 md:pr-4"
          :name="$t(`validatedata.street_number`)"
          :optional="true"
          :type="'text'"
          :inputName="'street_number'"
          :minLength="'1'"
          :maxLength="'20'"
          :value="computedStreetNumber || streetNumber"
          @inputChanged="setStreetNumber($event)"
          :browserAutocomplete="false"
          :autocompleteData="{ name: 'street_number', autocomplete: 'off' }"
        />

        <form-input
          v-if="showInputs || input.isFailing"
          :ref="input.name"
          @inputChanged="inputChanged($event, input)"
          @autocompleteSelect="autocompleteSelect($event, input)"
          class="mb-6"
          :class="{
            'md:w-3/5': input.name === 'address',
          }"
          :key="input.index"
          :active="input.active == 'true'"
          :optional="input.required === 'false'"
          :name="$t(`validatedata.${input.name}`)"
          :countryCode="selectedCountry"
          :type="input.type"
          :value="input.value"
          :options="input.options"
          :inputName="input.name"
          :minLength="input.minLength"
          :maxLength="input.maxLength"
          :documentType="documentTypeSelected"
          :documentNationality="nationalitySelected"
          :hasSimilarGuestError="hasSimilarGuestError"
          placeholder="..."
          :allowsFreeText="input.allowsFreeText || false"
          :check-on-start="!queryParams.manualProcess || Boolean(input.value)"
          :disableFutureDates="
            inputsWithDissableFutureDates.includes(input.name)
          "
          :inputError="inputErrorInfo"
          :debounce="input.name === 'document_number'"
          :disabled="input.disabled"
          :browserAutocomplete="enableBrowserAutocomplete"
          :autocompleteData="input.autocompleteData"
        />
      </div>
      <!-- eslint-enable-->
      <btn data-test="validate-data-button" @click.native="checkForm()"
        >{{ $t("validatedata.button") }}
      </btn>
      <go-back-link :text="$t('shared.previousStep')" />
    </div>
  </div>
</template>
<script>
import startLoading from "../mixins/startLoading";
import stopLoading from "../mixins/stopLoading";
import modal from "@/components/shared/modal.component";
import btn from "@/components/shared/button.component";
import titleComponent from "@/components/shared/title.component";
import validateDataCard from "../components/shared/validateDataCard.vue";
import formInput from "@/components/search/formInput.component";
import { cloneDeep } from "lodash";
import moment from "moment";
import debounce from "lodash/debounce";
import { mapState, mapActions, mapGetters } from "vuex";
import redirect from "@/mixins/redirect";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import { Geo } from "@aws-amplify/geo";
import { getDifference, sanitizeString } from "@/utils/stringUtils.js";
import repository from "../repository/repositoryFactory";
import { provinces } from "@/utils/ccaa";
import { countries, countryNamesArray } from "@/utils/countries";
import { nextTick } from "vue";
import { EMITTED_VALUE } from "@/mixins/dateFormat.js";
import sensibleData from "../mixins/sensibleData.js";
import dateFormat from "../mixins/dateFormat";
import { Tracker } from "@/tracker";
import goBackLink from "@/components/shared/goBackLink.component";
import {getSuggestionFilters} from "@/utils/address"

export default {
  name: "validateData",
  mixins: [
    startLoading,
    stopLoading,
    browserHistoryManagement,
    redirect,
    dateFormat,
    sensibleData,
  ],
  components: {
    modal,
    titleComponent,
    formInput,
    btn,
    goBackLink,
    validateDataCard,
  },
  data: () => {
    return {
      rendered: false,
      inputDataList: [],
      originalInputDataList: [],
      autocompleteOptions: [],
      valuesWithMuchDifference: [],
      allProvinces: provinces,
      selectedCCAA: null,
      selectedProvince: null,
      selectedCountry: null,
      countries: [],
      countryNamesArray: [],
      addressData: {},
      modalShown: true,
      inputsWithDissableFutureDates: ["birthday_date"],
      inputErrorInfo: {},
      nationalitySelected: null,
      documentTypeSelected: null,
      validationErrors: null,
      similarGuest: {},
      hasSimilarGuestError: false,
      spanishLanguageIso3: "ESP",
      showReceptionCard: false,
      showInputs: true,
      streetNumber: null,
      originalDocumentTypeOptions: [],
      messageError: null,
      badLocationCountries: ["JPN", "CHN"],
    };
  },
  computed: {
    ...mapState("guest", { guestList: "list" }),
    ...mapState("brand", ["brandId", "config", "country"]),
    ...mapState("brand", { placeCountry: "country" }),
    ...mapState("modal", { modalName: "name" }),
    ...mapState("reservations", ["reservationSelected"]),
    ...mapState("queryParams", { queryParams: "data" }),
    ...mapState("scan", { scanData: "data", entryPointView: "entryPointView" }),
    ...mapGetters("app", ["isReceptionMode", "getPreviousComponent"]),
    ...mapGetters("scan", { getScanData: "getScanData" }),
    ...mapGetters("guest", {
      guestData: "getSelectedGuest",
      getHolderGuest: "getHolderGuest",
      guestLiteData: "getLiteSelectedGuestData",
    }),
    computedStreetNumber() {
      return this.addressData?.addressNumber ?? null;
    },
    manualOrScan() {
      return this.queryParams.manualProcess === true
        ? this.$t("scan.addDataManually")
        : this.$t("validatedata.title");
    },
    isLoading() {
      return this.$store.state.loading.loading;
    },
    filteredProvinces() {
      return this.allProvinces.filter(
        (province) => this.selectedCCAA == province.ccaaCode
      );
    },
    debounceManageAddressInfo() {
      return debounce(this.manageAddressInfo, 200);
    },
    debounceAutofillFields() {
      return debounce(this.autofillFields, 200);
    },
    enableBrowserAutocomplete() {
      return this.queryParams.manualProcess ?? false;
    },
  },

  
  async created() {
    this.countryNamesArray = countryNamesArray; 
    if (this.isReceptionMode && this.guestData?.date_of_issue && this.entryPointView === "ValidateData") {
      this.showReceptionCard = true;
      this.showInputs = false;
    }
    this.streetNumber = this.guestData?.address?.street_number || null;

    let validateDataInputs = cloneDeep(
      this.config.identification.validate_data_scan[0]
    );

    if (this.queryParams.manualProcess) {
      this.inputDataList = await this.getInputsForm(
        this.guestData || [], // Guest object
        validateDataInputs // Hotel config fields
      );
    } else {
      this.originalInputDataList = await this.getInputsForm(
        this.guestData || [], // Guest object
        validateDataInputs // Hotel config fields
      );
      this.checkInputIsFailing(this.originalInputDataList);
      // Sort original input list so differences modal compares the inputs properly
      this.originalInputDataList = this.sortItemsByPositionIndex(
        this.originalInputDataList
      );
      this.inputDataList = cloneDeep(this.originalInputDataList);
      // If you log this.inputDataList in this line, it will not reflect what it is at this point in the code. Instead, it will log the result of this.inputDataList AFTER it has passed through the forEach. Eg: options are changed from an array of strings to an array of objects.
    }

    this.inputDataList = this.sortItemsByPositionIndex(this.inputDataList);

    await this.inputDataList.forEach(
      function (input) {

        if (
          this.guestData?.holder &&
          this.config.reservation_holder_not_modifiable
        ) {
          // For inputs name and surname, add the guest data instead of scan data when the user is the guest
          if (input.name === "name" && this.guestData?.name) {
            input.value = this.guestData?.name;
            input.disabled = true;
          }
          if (input.name === "surname" && this.guestData?.surname) {
            input.value = this.guestData?.surname;
            input.disabled = true;
          }
        }

        if (
          this.config.second_surname_required_for_spanish &&
          input.name === "second_surname"
        ) {
          let nationality = this.inputDataList.find(
            (i) => i.name == "nationality"
          );
          nationality?.value == this.spanishLanguageIso3
            ? (input.required = "true")
            : (input.required = "false");
        }

        const guestHolder = this.getHolderGuest;
        if (
          input?.fill_from_holder === "true" &&
          !input.value &&
          !this.guestData?.holder &&
          guestHolder &&
          !(input.not_fill_on_reception === "true" && this.isReceptionMode)
        ) {
          if (input.name === "telephone" && guestHolder?.telephone) {
            input.value =
              typeof guestHolder.telephone === "object"
                ? guestHolder.telephone.value
                : guestHolder.telephone;
          } else if (
            input.name === "postal_code" &&
            guestHolder?.address?.postal_code
          ) {
            input.value = guestHolder.address.postal_code;
          } else if (input.name === "address" && guestHolder?.address?.street) {
            input.value = guestHolder.address.street;
            this.streetNumber = guestHolder.address?.street_number || null;
          } else if (
            input.name === "municipality" &&
            guestHolder?.address?.city
          ) {
            input.value = guestHolder.address.city;
          } else if (
            input.name === "residence_country" &&
            guestHolder?.residence_country
          ) {
            const country = countries.find(
              (needle) =>
                guestHolder.residence_country.toLowerCase() ==
                needle.name.toLowerCase()
            )?.value;
            input.value = country ?? guestHolder.residence_country;
          } else if (input.name === "region" && guestHolder?.address?.region) {
            input.value = guestHolder.address.region;
          } else if (
            input.name === "subregion" &&
            guestHolder?.address?.subregion
          ) {
            input.value = guestHolder.address.subregion;
          } else if (guestHolder[input.name]) {
            input.value = guestHolder[input.name];
          }
        }

        if (input.name === "address") {
          input.options = this.autocompleteOptions;
          input.allowsFreeText = true;
          input.value =
            typeof input?.value === "object" && !input?.value?.street
              ? ""
              : input.value;
        }
        if (input.name === "CCAA") {
          let residence_country = this.inputDataList.find(
            (i) => i.name == "residence_country"
          );
          residence_country?.value == this.spanishLanguageIso3 ||
          this.guestData?.residence_country == this.spanishLanguageIso3 ||
          this.addressData?.country == this.spanishLanguageIso3
            ? (input.active = "true")
            : (input.active = "false");
        }
        if (input.name === "region" || input.name === "subregion") {
          let residence_country = this.inputDataList.find(
            (i) => i.name == "residence_country"
          );
          residence_country?.value !== this.spanishLanguageIso3 ||
          (this.guestData?.residence_country &&
            this.guestData?.residence_country !== this.spanishLanguageIso3) ||
          (this.addressData?.country &&
            this.addressData?.country !== this.spanishLanguageIso3)
            ? (input.active = "true")
            : (input.active = "false");
        }
        if (input.name === "province") {
          if (input.active === "false") {
            // Set input.active to null if it's not enabled in private
            input.active = null;
          }
          let ccaaInput = this.inputDataList.find((i) => i.name == "CCAA");
          if (
            ccaaInput.value !== "0" &&
            ccaaInput.value !== null &&
            input.active !== null &&
            ccaaInput.active == "true"
          ) {
            input.active = "true";
          } else if (input.active === null) {
            input.active = null;
          } else {
            input.active = "false";
          }
        }

        if (input.name === "document_support_number") {
          this.showSupportNumber();
        }

        if (input.name === "kinship") {
          const paxType = this.guestData?.pax_type;

          if (paxType !== "CH" && paxType !== "BB" && paxType !== "JR") {
            input.active = "false";
          }
        }

        if (input.options) {
          input.options.forEach(
            function (option, index, options) {
              if (
                input.name === "nationality" ||
                input.name === "residence_country"
              ) {
                options[index] = {
                  value: option.value,
                  name: this.$t(`countries.${option.value}`),
                };
                this.countries = options;
              } else if (input.name === "CCAA" || input.name === "province") {
                options[index] = {
                  value: option.code,
                  name: option.value,
                };
              } else if (input.name === "address") {
                options[index] = {
                  value: input.value,
                  name: input.value,
                };
              } else {
                options[index] = {
                  value: option,
                  name: this.$t(`validatedata.${option}`),
                };
              }
            }.bind(this)
          );
        }
      }.bind(this)
    );

    // Store all of the original options.
    this.originalDocumentTypeOptions = this.inputDataList.find(
      (item) => item.name === "document_type"
    ).options;

    // If nationality is set, filter the available document types
    const nationalityInput = this.inputDataList.find(
      (item) => item.name === "nationality"
    );

    if (nationalityInput.value) {
      this.setDocumentTypeOptions(nationalityInput.value, false);
    }

    this.checkInputIsFailing(this.inputDataList);
    if (
      this.isReceptionMode &&
      this.inputValidation(this.inputDataList).length == 0 && //this checks if it has error, if it does it stays on page
      this.inputDataList.filter(
        (input) => !input.value && input.hasError && input.active != "true"
      ).length == 0 &&
      this.getPreviousComponent !== "Documents" &&
      this.guestData.date_of_issue
    ) {
      return await this.submitForm(this.getGuestData());
    }

    // Log filled values when user scans
    if (!this.queryParams.manualProcess) {
      this.logFilledFieldsVsAllFields();
    }

    // To ensure once all inputs have changed, modal does not pop up
    this.modalShown = false;
    this.rendered = true;
    await this.stopLoading();
    Tracker.recordCustomEvent("guest_form_start", {
      guest: this.guestLiteData,
    });
  },
  methods: {
    ...mapActions("guest", ["UPDATE_GUEST", "SELECT_GUEST", "SIMILAR_GUESTS"]),
    ...mapActions("modal", [
      "VISIBLE",
      "SET_TITLE",
      "SET_NAME",
      "SET_TYPE",
      "CLEAR_STATE",
    ]),
    ...mapActions("loading", ["LOADING"]),
    checkInputIsFailing(inputs) {
      if (this.isReceptionMode) {
        for (const input of inputs) {
          if (!input.active) return;
          input.isFailing = input.hasError || !input.value;
        }
      }
    },
    setStreetNumber(value) {
      this.streetNumber = value.value;
    },
    showFailingInputs(input) {
      return input.hasError || !input.value ? true : false;
    },
    showAllInputs() {
      this.showReceptionCard = !this.showReceptionCard;
      this.showInputs = true;
    },
    logFilledFieldsVsAllFields() {
      const getAllInputs = this.inputDataList.length;
      const getFilledInputs = this.inputDataList.filter(
        (input) => input.value
      ).length;
      const filledFieldsRatio = Math.round(
        (getFilledInputs / getAllInputs) * 100
      );
      console.info(`Validate data field ratio`, {
        fieldCount: getAllInputs,
        filledFieldsCount: getFilledInputs,
        ratio: filledFieldsRatio + "%",
      });
    },
    modalClosed() {
      this.CLEAR_STATE();
      return this.redirect({ name: "Status" });
    },

    async inputChanged(value, input) {
      if (
        input.value === value.value &&
        input.hasError === value.error &&
        input.name !== "CCAA"
      ) {
        return;
      }
      input.value = value.value;
      input.hasError = value.error;

      if (input.name === "document_number") {
        const similarGuest = await this.SIMILAR_GUESTS(this.getGuestData());
        const hasError = similarGuest.coincidences?.some(
          (coincidence) =>
            coincidence.fieldName === "document_number" &&
            coincidence.similarity === 1
        );
        this.hasSimilarGuestError = hasError;
        input.hasError = hasError;
      }

      if (input.name === "residence_country" && this.selectedCountry === null) {
        this.selectedCountry = input.value;
      }
      if (input.type == "autocomplete") {
        if (input.name == "residence_country" || input.name == "nationality") {
          const sanitizedCountryNames = this.countryNamesArray.map(
            (country) => ({
              names: country.names.map((name) =>
                sanitizeString(name.toLowerCase())
              ),
              code: country.code,
            })
          );
          const countriesFound = sanitizedCountryNames.filter((country) =>
            country.names.some((name) =>
              sanitizeString(name.toLowerCase()).includes(
                sanitizeString(input.value.toLowerCase())
              )
            )
          );
          const matchingCodes = countriesFound.map((country) => country.code);
          input.options = this.countries.filter((country) =>
            matchingCodes.includes(country.value)
          );
        }
      }
      let postalCode;
      switch (input.name) {
        case "postal_code":
        case "municipality":
        case "region":
        case "subregion":
          this.debounceAutofillFields(input);
          break;
        case "residence_country":
          this.activeCCAA(input.value);
          break;
        case "telephone":
          input.dialCode = value.dialCode;
          input.countryCode = value.country;
          break;
        case "CCAA":
          postalCode = this.inputDataList.find(
            (item) => item.name == "postal_code"
          );
          if (
            input.value != "0" &&
            this.selectedCCAA &&
            input.value != this.selectedCCAA &&
            postalCode
          ) {
            this.$refs.postal_code[0]?.$refs?.component?.inputChanged("");
          }

          if (input.value != "0") {
            this.selectedCCAA = input.value ?? "0";
          }

          if (this.inputDataList.find((item) => item.name == "province")) {
            this.activeProvince();
          }
          break;
        case "address":
          this.debounceManageAddressInfo(input);
          break;
        case "document_type":
          this.documentTypeSelected = input.value;
          this.showSupportNumber();
          this.showFiscalCode();
          break;
      }
      if (
        this.isSpanishPostalCode(input) // Spanish provinces are matched with first 2 numbers
      ) {
        let ccaaData = {
          ...this.allProvinces.find(
            (option) => option.code == input.value.slice(0, 2)
          ),
        };

        this.selectedCCAA = ccaaData.ccaaCode ?? "0";
        this.selectedProvince = ccaaData.code ?? "0";

        if (this.inputDataList.find((item) => item.name == "CCAA")) {
          this.$refs.CCAA[0]?.$refs?.component?.inputChanged({
            value: ccaaData.ccaaCode ?? "0",
          });
        }
      }
    },
    async manageAddressInfo(input) {
      let inputAddress = this.inputDataList.find(
        (input) => input.name == "address"
      );
      let suggestionFilters = {};
       if (this.selectedCountry) {
        suggestionFilters = getSuggestionFilters(this.selectedCountry)}

      // Start request at 5 chars (For now)
      if (
        input.value.length > 5 &&
        !this.config?.disable_address_autocomplete  &&
        (!this.config?.disable_only_address_autocomplete || input.name !== "address") &&
        !this.badLocationCountries.includes(this.selectedCountry)
      ) {
        this.autocompleteOptions = await Geo.searchForSuggestions(
          input.value,
          suggestionFilters
        );
      }
      inputAddress.options.splice(0);
      this.autocompleteOptions.forEach((option) => {
        const optionValue = option?.text ?? option;
        return inputAddress.options.push({
          name: optionValue,
          value: optionValue,
        });
      });
    },

    async autofillFields(input) {
      if (input.mappedFromAutocomplete) {
        // Set back to false to distinguish between the input being filled with the autocompleteSelect from the input being manually filled
        input.mappedFromAutocomplete = false;
      } else {
        if (
          this.selectedCountry &&
          !this.config?.disable_address_autocomplete &&
          (!this.config?.disable_only_address_autocomplete || input.name !== "address") &&
          !this.badLocationCountries.includes(this.selectedCountry) &&
          input.value &&
          input.hasError !== true
        ) {
          const searchOptionsWithBiasPosition = {
            countries: [this.selectedCountry],
          };

          const locationData = await Geo.searchByText(
            input.value,
            searchOptionsWithBiasPosition
          );
          let optionsForCountry = locationData.filter(
            (location) => location.country === this.selectedCountry
          );
          if (optionsForCountry.length === 1) {
            const propertiesToUpdate = [
              "postalCode",
              "municipality",
              "region",
              "subRegion",
            ];

            // Update addressData
            optionsForCountry[0] = this.formatSpecialCases(
              optionsForCountry[0]
            );

            propertiesToUpdate.forEach((prop) => {
              if (optionsForCountry[0][prop] !== undefined) {
                this.addressData[prop] = optionsForCountry[0][prop];
              }
            });
            const data_map = {
              postal_code: optionsForCountry[0].postalCode,
              municipality: optionsForCountry[0].municipality,
              region: optionsForCountry[0].region,
              subregion: optionsForCountry[0].subRegion,
            };

            let newInputDataList = this.inputDataList.map(
              (inputItem, index) => {
                if (data_map[inputItem.name]) {
                  const newValue = data_map[inputItem.name];
                  inputItem.value = newValue;
                  // The line below triggers the reactivity that allows the fields to update
                  inputItem.index = "key-" + (inputItem.index ?? index + 1);
                }
                return inputItem;
              }
            );

            this.inputDataList = newInputDataList;
          }
        }
      }
    },

    async setDocumentTypeOptions(nationality, inputChanged = true) {
      const documentTypeInput = this.inputDataList.find(
        (item) => item.name === "document_type"
      );

      const currentCountrySchengenZone = this.checkSchegenZone(this.country);
      const nationalityISO2 = this.getISO2Value(nationality);
      const nationalitySchengenZone = this.checkSchegenZone(nationalityISO2);
      const nationalityMatchesHotel = nationalityISO2 === this.country;

      const identityCardIsAllowed =
        nationalityMatchesHotel ||
        (currentCountrySchengenZone && nationalitySchengenZone);
      const drivingLicenseIsAllowed =
        nationalityMatchesHotel && this.config.allow_driving_license;

      if (inputChanged) {
        // Reset the value of documentType to "" ensure that if guest changes nationality, documentType adjusts properly
        documentTypeInput.value = "";
      }
      let options;
      let allowPassport = true;

      if ( //We don't allow passports from the country of the brand if config not_allow_passports_from_country_brand is true
        this.config.not_allow_passports_from_country_brand &&
        nationalityISO2 === this.country
      ) {
        allowPassport = false;
        if(
          this.inputDataList.find((item) => item.name === "document_type") === 'passport' //We only clean case if document_type is passport
        ){
          // Reset the value of documentType to "" ensure that
          this.inputDataList.find(
            (item) => item.name === "document_type"
          ).value = "";
        }
        this.showFiscalCode(); //Update fiscal code visibility on initialization
        
      }
      options = this.originalDocumentTypeOptions.filter((option) => {
        return (
          (identityCardIsAllowed && option.value === "identity_card") ||
          (drivingLicenseIsAllowed && option.value === "driving_license") ||
          (allowPassport && option.value === "passport") ||
          option.value === "residence_permit"
        );
      });

      documentTypeInput.options = options;
    },

    async autocompleteSelect(value, input) {
      let data_map;
      if (input.name == "address") {
        data_map = await this.handleChangeAddress(value);
      }

      if (input.name == "nationality") {
        this.setDocumentTypeOptions(value.value);
      }

      if (
        input.name == "nationality" &&
        this.config.second_surname_required_for_spanish
      ) {
        this.toggleSecondSurnameMandatory(value.value);
      }

      if (input.name == "residence_country" || input.name == "nationality") {
        data_map = {
          [input.name]: () => value.value,
        };
      }

      // If fill nationality and the residence country is not set, update residence country with the same value
      const residenceCountryInput = this.inputDataList.find(
        (item) => item.name == "residence_country"
      );
      if (
        input.name == "nationality" &&
        residenceCountryInput &&
        !residenceCountryInput?.value
      ) {
        data_map = {
          ...data_map,
          ["residence_country"]: () => value.value,
        };
      }

      let newInputDataList = this.inputDataList.map((input, index) => {
        if (data_map[input.name]) {
          let newValue = data_map[input.name](input);
          input.value = newValue;
          input.hasError = value.error;
          input.mappedFromAutocomplete = true;
          if (input.name == "province") {
            this.options = this.filteredProvinces;
          }
          if (input.name == "nationality") {
            this.nationalitySelected = input.value;
            this.showSupportNumber();
            this.showFiscalCode();
          }
          if (input.name == "residence_country") {
            input.options = this.countries;
            this.selectedCountry = input.value;
            this.activeCCAA(input.value);
          }
          // TODO: Change this to use vue reactivity, for now this works
          input.index = "key-" + (input.index ?? index + 1);
        }
        return input;
      });

      this.inputDataList = newInputDataList;
      // Clean options
      this.autocompleteOptions = [];
    },

    async getInputsForm(data, validateDataInputs) {
      const address = this.manageAddress(data?.address);

      const addressInput = validateDataInputs.find(
        (item) => item.name == "address"
      );

      // Do initial calls to GEO with the adress info provided if the input is activated
      if (
        address &&
        addressInput &&
        addressInput?.active &&
        !this.config?.disable_address_autocomplete &&
        (!this.config?.disable_only_address_autocomplete || addressInput.name !== "address")
      ) {
        try {
          let suggestionFilters = {};

          const countryCode = countries.find(
            (needle) =>
              data?.residence_country.toLowerCase() ==
              needle.value.toLowerCase()
          )?.value;

          if (countryCode) {
            suggestionFilters = {
              ...suggestionFilters,
              countries: [countryCode],
            };
          }

          if (!this.badLocationCountries.includes(this.selectedCountry)) {
            this.autocompleteOptions = await Geo.searchForSuggestions(
              address,
              suggestionFilters
            );
            // * If there's options, search by the first one
            if (this.autocompleteOptions.length > 0) {
              let selectedOption = await Geo.searchByText(
                this.autocompleteOptions[0]?.text ?? this.autocompleteOptions[0]
              );
              // ACI:699 | https://hotelinking.atlassian.net/browse/ACI-699
              console.info("ValidateData address comparation", {
                guestAddress: address,
                awsResult: selectedOption[0] ?? {},
              });

              this.addressData = selectedOption[0] ?? null;
            }
          }
        } catch (error) {
          console.error("Validate Data address error", { error });
        }
      }

      return validateDataInputs.map((validateDataInput) => {
        // TODO: improve this, too many if
        let inputValue;
        inputValue = data[validateDataInput?.name] || "";

        if (validateDataInput.name === "municipality") {
          inputValue = this.addressData?.municipality || data?.address?.city || "";
        }
        // To avoid having "Invalid date" value on inputs
        if (validateDataInput.type === "date" && !inputValue) {
          inputValue = null;
        }

        if (validateDataInput.name === "postal_code") {
          inputValue =
            this.addressData?.postalCode ??
            data?.address?.postalCode ??
            data?.address?.postal_code;
        }

        if (
          validateDataInput.name === "CCAA" &&
          (this.addressData?.country === this.spanishLanguageIso3 ||
            data?.residence_country === this.spanishLanguageIso3)
        ) {
          inputValue =
            this.getOptionByName(
              validateDataInput.options,
              this.addressData?.region ?? data?.address?.province
            )?.code ?? "0";
        }

        if (
          validateDataInput.name === "region" &&
          (this.addressData?.country !== this.spanishLanguageIso3 ||
            data?.residence_country !== this.spanishLanguageIso3)
        ) {
          inputValue = this.addressData?.region;
        }

        if (
          validateDataInput.name === "subregion" &&
          (this.addressData?.country !== this.spanishLanguageIso3 ||
            data?.residence_country !== this.spanishLanguageIso3)
        ) {
          inputValue = this.addressData?.subregion;
        }

        if (
          validateDataInput.name === "province" &&
          (this.addressData?.country === this.spanishLanguageIso3 ||
            data?.residence_country === this.spanishLanguageIso3)
        ) {
          inputValue =
            this.getOptionByName(
              validateDataInput.options,
              this.addressData?.subRegion
            )?.code ?? "0";
        }

        if (validateDataInput.name === "nationality") {
          this.nationalitySelected = data?.nationality;
        }
        if (validateDataInput.name === "document_type") {
          this.documentTypeSelected = data?.document_type;
        }

        if (validateDataInput.name === "residence_country") {
          inputValue = this.addressData?.country ?? data?.residence_country;
          this.selectedCountry = inputValue;
        }

        if (
          validateDataInput.name === "telephone" &&
          typeof inputValue === "object"
        ) {
          inputValue = inputValue?.value ?? "";
        }

        if (inputValue != null) {
          if (validateDataInput.type == "date") {
            inputValue = moment(inputValue, true).format(EMITTED_VALUE);
          }

          if (validateDataInput.name === "address") {
            inputValue = address;
          }

          validateDataInput.value = inputValue;
        } else {
          validateDataInput.value = null;
        }
        if (
          validateDataInput.type === "select" ||
          validateDataInput.type === "autocomplete"
        ) {
          validateDataInput.hasError = false;
        }
        if (validateDataInput.name === "name") {
          validateDataInput.autocompleteData = {
            name: "firstName",
            autocomplete: "given-name",
          };
        } else if (validateDataInput.name === "surname") {
          validateDataInput.autocompleteData = {
            name: "lastName",
            autocomplete: "family-name",
          };
        } else if (validateDataInput.name === "address") {
          validateDataInput.autocompleteData = {
            name: "street-address",
            autocomplete: "street-address",
          };
        } else {
          validateDataInput.autocompleteData = {
            name: validateDataInput.name,
            autocomplete: "off",
          };
        }
        return validateDataInput;
      });
    },

    async checkForm() {
      this.checkInputIsFailing(this.inputDataList);
      return await this.submitForm(this.getGuestData());
    },

    async submitForm(guestDataToSet) {
      try {
        this.checkInputIsFailing(this.inputDataList);
        // * Validate inputs on click
        this.validationErrors = this.inputValidation(this.inputDataList);
        if (this.validationErrors.length > 0) {
          console.error("Submit validation errors", {
            inputs: this.validationErrors,
          });
          if (this.rendered) {
            if (this.validationErrors[0].type == "date") {
              const element =
                this.$refs[this.validationErrors[0].name][0].$refs.component
                  .$refs[`datepicker-${this.validationErrors[0].name}`];
              if (element) {
                element.focus();
              }
            } else {
              const element = this.$refs[
                this.validationErrors[0].name
              ][0].$refs.component.$el.querySelector(
                'input:not([style*="display: none"])'
              );
              if (element) {
                element.focus();
              }
            }

            this.validationErrors.forEach((input) => {
              const childComponent = this.$refs[input.name][0].$refs?.component;
              if (input.type !== "autocomplete") {
                // Remember the current value. If empty, the space will make the input dirty so it shows an error, then it is trimmed
                childComponent.inputChanged(input.value || " ");
              } else {
                childComponent.handleInputChange();
              }
            });
          }
          return;
        }

        if (!Object.keys(this.similarGuest).length) {
          this.similarGuest = await this.SIMILAR_GUESTS(this.getGuestData());

          if (
            this.similarGuest.coincidences?.some(
              (coincidence) =>
                coincidence.fieldName === "document_number" &&
                coincidence.similarity === 1
            )
          ) {
            return this.handleSimilarGuestInReservation();
          }
        }
        // * Email validation
        let email = this.inputDataList.find((needle) => needle.name == "email");
        if (email?.active == "true" && email?.value) {
          const apiEmail = repository.get("email");
          let emailValidation;

          try {
            emailValidation = await apiEmail.validateEmail(
              email?.value,
              this.brandId
            );
          } catch (error) {
            console.warn("Email validation error", { error });
            emailValidation = {
              valid: false,
            };
          }
          // * Email validation is passed to component
          if (this.rendered) {
            await this.$refs.email[0]?.$refs.component.emailValidation(
              emailValidation
            );
          }
          if (!emailValidation.valid) {
            await this.showModal({
              type: "error",
              name: "emailValidation",
              title: this.$t("senddocuments.emailModalTitle"),
            });
            return;
          }
        }
        if (!this.queryParams.manualProcess) {
          this.valuesWithMuchDifference = this.getInputsWithMuchDifference(
            this.originalInputDataList,
            this.inputDataList
          );
        }
        guestDataToSet = {
          ...guestDataToSet,
          address: {
            street: guestDataToSet.address || null,
            street_number: guestDataToSet.street_number || null,
            city: guestDataToSet.municipality || null,
            CCAA: guestDataToSet.CCAA || null,
            country: guestDataToSet.residence_country || null,
            province: guestDataToSet.province || null,
            region: guestDataToSet.region || null,
            subregion: guestDataToSet.subregion || null,
            postal_code: guestDataToSet.postal_code || null,
          },
        };

        this.UPDATE_GUEST(guestDataToSet);

        console.info("Guest data set", { guestDataToSet });
        if (this.valuesWithMuchDifference.length === 0) {
          return this.redirectNextStep();
        } else {
          console.warn(
            "Differences found between Document Scan and User",
            this.sanetizeDifferencesLog(this.valuesWithMuchDifference)
          );

          await this.showModal({
            type: "info",
            name: "differences",
            title: "",
          });
        }
      } catch (error) {
        if (this.isReceptionMode) {
          this.showAllInputs();
        }
        console.error("Validate Data error", {
          guestDataToSet,
          error: error.message,
        });
      }
    },
    async closeModal() {
      await this.VISIBLE(false);
    },

    redirectNextStep() {
      this.closeModal();
      Tracker.recordCustomEvent("guest_form_completed", {
        guest: this.guestLiteData,
      });

      return this.redirect({ name: "ChildsData" });
    },

    async handleSimilarGuestInReservation() {
      // Don't allow already checked-in guests nor scanned guest
      if (
        this.similarGuest?.processCompleted ||
        this.similarGuest?.validated ||
        !this.queryParams.manualProcess
      ) {
        return this.showModal({
          type: "error",
          name: "errorModal",
          title: this.$t("scan.error.similarGuestError"),
        });
      }
      // Children have its own form, unless config is setted
      if (
        (this.similarGuest?.pax_type == "CH" &&
          !this.config.scan_children_like_adults) ||
        (this.similarGuest?.pax_type == "BB" &&
          !this.config.scan_children_like_adults)
      ) {
        await this.SELECT_GUEST(this.similarGuest?.uuid);
        return this.redirect({ name: "ChildForm" });
      }

      await this.handleSelectedGuest(this.similarGuest);
    },

    async handleSelectedGuest(guest) {
      await this.VISIBLE(false);

      await this.SELECT_GUEST(guest?.uuid);

      await this.checkInputIsFailing(this.inputDataList);
      await this.submitForm(this.getGuestData());
    },
    //! Filters inputs that are not valid
    inputValidation(inputs) {
      return inputs.filter((input) => {
        return (
          input.active === "true" &&
          (input.hasError || (input.required === "true" && !input.value))
        );
      });
    },

    getGuestData() {
      const streetNumber = this.streetNumber;
      return {
        ...this.inputDataList.reduce(function (guestObject, input) {
          guestObject[input.name] = input.value;
          if (input.name === "telephone") {
            guestObject[input.name] = {
              value: input.value || "",
              dialCode: input.dialCode || "",
              countryCode: input.countryCode || "",
            };
          }
          if (input.name === "address") {
            guestObject.street_number = streetNumber;
          }
          return guestObject;
        }, {}),
      };
    },
    getInputsWithMuchDifference(original, changed) {
      const originalFiltered = original.filter(
        (originalInput) => originalInput.value
      );
      const changedFiltered = changed.filter((changedInput) =>
        originalFiltered.some((input) => input.name === changedInput.name)
      );

      return originalFiltered.reduce((resultObject, originalInput, index) => {
        let diff = 1;
        // If input is province or CCAA and its original value was 0 (no data from scan), don't calculate diff
        if (
          !(
            (originalInput.name === "province" ||
              originalInput.name === "CCAA") &&
            originalInput.value == "0"
          ) &&
          !(
            (changedFiltered[index].name === "province" ||
              changedFiltered[index].name === "CCAA") &&
            changedFiltered[index].value == "0"
          )
        ) {
          diff = getDifference(
            originalInput.value?.toString(),
            changedFiltered[index].value?.toString()
          );
        }

        // Set a limit of 40% difference between the two strings
        if (diff < 0.4) {
          resultObject.push([originalInput, changedFiltered[index]]);
        }
        return resultObject;
      }, []);
    },
    formatInputValue(input) {
      if (input.name === "gender") {
        return this.$t(`validatedata.selectedGender.${input.value}`);
      }

      if (input.name == "residence_country" || input.name == "nationality") {
        return this.countries.find((needle) => input.value == needle.value)
          ?.name;
      }

      if (input.type === "date" && input.value) {
        return moment(input.value, true).format(this.countryFormat());
      }
      if (input.name === "CCAA" || input.name === "province") {
        let option = input.options.find(
          (needle) => (needle.code ?? needle.value) == input.value
        );
        if (option) {
          return option.name ?? option.value;
        }
        return "0";
      }

      if (input.name === "document_type") {
        return this.$t(`validatedata.${input.value}`);
      }
      return input.value;
    },
    showSupportNumber() {
      const documentSupportNumber = this.inputDataList.find(
        (input) => input.name == "document_support_number"
      );
      const nationality = this.inputDataList.find(
        (input) => input.name == "nationality"
      )?.value;
      const residenceCountry = this.inputDataList.find(
        (input) => input.name == "residence_country"
      )?.value;
      const documentType = this.inputDataList.find(
        (input) => input.name == "document_type"
      ).value;

      //check if documentSupportNumber exists and enable input only if it's a Spanish DNI or NIE
      if (documentSupportNumber) {
        (documentType === "identity_card" || documentType === "residence_permit") && (nationality === "ESP" || residenceCountry === "ESP")
          ? this.toggleInput(documentSupportNumber, "enable")
          : this.toggleInput(documentSupportNumber, "disable");
      }
    },
    showFiscalCode() {
      const fiscalCode = this.inputDataList.find(
        (input) => input.name == "fiscal_code"
      );
      const issuingCountry = this.inputDataList.find(
        (input) => input.name == "nationality"
      ).value;
      const documentType = this.inputDataList.find(
        (input) => input.name == "document_type"
      ).value;

      if (fiscalCode) {
        issuingCountry === "ITA" && documentType === "identity_card"
          ? this.toggleInput(fiscalCode, "enable")
          : this.toggleInput(fiscalCode, "disable");
      }
    },
    activeCCAA(value) {
      let ccaa = this.inputDataList.find((input) => input.name == "CCAA");
      let region = this.inputDataList.find((input) => input.name == "region");
      let subregion = this.inputDataList.find(
        (input) => input.name == "subregion"
      );
      let provinceInput = this.inputDataList.find(
        (input) => input.name == "province"
      );
      // check if ccaa exists
      if (ccaa) {
        if (value == this.spanishLanguageIso3) {
          this.toggleInput(ccaa, "enable");

          // If the postal code was filled before CCAA active (filled before residence country), we trigger the
          // input changed of postal code to retrieve the CCAA and province automatically
          const postalCode = this.inputDataList.find(
            (input) => input.name == "postal_code"
          );

          if (postalCode?.value && !ccaa?.value) {
            nextTick(() => {
              this.$refs.postal_code[0]?.$refs?.component?.inputChanged(
                postalCode.value
              );
            });
          }
        } else {
          this.toggleInput(ccaa, "disable");
        }
      }

      if (region) {
        if (value == this.spanishLanguageIso3) {
          this.toggleInput(region, "disable");
        } else {
          this.toggleInput(region, "enable");
        }
      }

      if (subregion) {
        if (value == this.spanishLanguageIso3) {
          this.toggleInput(subregion, "disable");
        } else {
          this.toggleInput(subregion, "enable");
        }
      }

      // check if province exists
      if (provinceInput) {
        if (provinceInput.active !== null) {
          this.toggleInput(provinceInput, "disable");
        }
      }
    },
    toggleSecondSurnameMandatory(value) {
      let secondSurnameInput = this.inputDataList.find(
        (input) => input.name == "second_surname"
      );
      if (value == this.spanishLanguageIso3) {
        secondSurnameInput.required = "true";
      } else {
        secondSurnameInput.required = "false";
      }
    },
    async activeProvince() {
      let provinceInput = this.inputDataList.find(
        (input) => input.name == "province"
      );
      let postalCode = this.inputDataList.find(
        (input) => input.name == "postal_code"
      );

      if (
        provinceInput &&
        this.selectedCCAA !== "0" &&
        this.selectedCCAA !== null
      ) {
        this.toggleInput(provinceInput, "disable");
        // Delete the input options and then introduce the code and name of each province
        provinceInput.options = [];

        await this.filteredProvinces.forEach((province, index) => {
          provinceInput.options[index] = {
            value: province.code,
            name: province.value,
          };
        });
        provinceInput.value = this.selectedProvince;

        if (
          !provinceInput.options.find(
            (province) => province.value == this.selectedProvince
          )
        ) {
          provinceInput.value = this.selectedProvince = "0";
        }

        // if postal_code is already set, use it to filter
        if (
          postalCode &&
          postalCode?.value != "" &&
          postalCode?.value.length >= 2
        ) {
          provinceInput.value = this.filteredProvinces.find(
            (province) => province.code == postalCode?.value.slice(0, 2)
          )?.code;
        }
        if (this.filteredProvinces.length === 1) {
          // If the region (CCAA) has only 1 province, select it automatically
          provinceInput.value = this.filteredProvinces[0].code;
        }
        this.toggleInput(provinceInput, "enable");
      } else if (provinceInput.active === null) {
        return;
      } else {
        this.toggleInput(provinceInput, "disable");
      }
    },

    getOptionByName(options, name, difference = 0.7) {
      if (!name || !options || options.length == 0) {
        return "0";
      }
      const sanetizedName = name.replace("-", " ");
      let result = options.find(
        (option) =>
          option.value == sanetizedName || option.name == sanetizedName
      );

      // TODO: add Euskadi to Pais Vasco literal => País Vasco/Euskadi
      if (sanetizedName == "Euskadi") {
        result = options.find(
          (option) =>
            option.value == "País Vasco" || option.name == "País Vasco"
        );
      }
      if (result) {
        return result;
      }
      // If text is not exactly the same as the option ex. Comunidad Valenciana ≠ Comunitat Valenciana
      return options.find(
        (option) =>
          getDifference(option.name || option.value, sanetizedName) > difference
      );
    },
    toggleInput(input, action) {
      if (action === "disable") {
        input.active = "false";
        input.value = null;
      } else if (action === "enable") {
        input.active = "true";
      }
    },
    async showModal(data) {
      await this.SET_TYPE(data.type);
      await this.SET_NAME(data.name);
      if (data.name === "errorModal") {
        await this.SET_TITLE("");
      } else {
        await this.SET_TITLE(data.title);
      }
      this.messageError = data.title;
      await this.VISIBLE(true);
    },

    formatSpecialCases(addressData) {
      if (addressData?.country === "IMN") {
        addressData.postalCode = addressData?.postalCode?.replace(/\s/g, "");
      }

      return addressData;
    },

    async handleChangeAddress(value) {
      // * Get address data
      let locationData = await Geo.searchByText(value.value);
      // * Set address
      this.addressData = locationData[0];
      this.addressData.region = this.normalizeRegionName(
        this.addressData.region
      );

      // * Active ccaa if country is ESP
      this.activeCCAA(this.addressData?.country);

      this.addressData = this.formatSpecialCases(this.addressData);

      // * Address data function map
      return {
        address: () => this.addressData?.street || null,
        street_number: () => this.addressData?.addressNumber || null,
        postal_code: () => this.addressData?.postalCode,
        residence_country: () => this.addressData?.country,
        CCAA: (input) => {
          if (this.addressData?.country == this.spanishLanguageIso3) {
            this.selectedCCAA =
              this.getOptionByName(input.options, this.addressData?.region)
                ?.value ?? "0";
            return this.selectedCCAA;
          }
          return "0";
        },
        region: () => this.addressData?.region || null,
        subregion: () => this.addressData?.subRegion || null,
        province: () => {
          if (this.addressData?.country == this.spanishLanguageIso3) {
            this.selectedProvince =
              this.getOptionByName(
                this.filteredProvinces,
                this.addressData?.subRegion,
                0.15
              )?.code ?? "0";
            return this.selectedProvince;
          }
          return "0";
        },

        municipality: () => this.addressData?.municipality || null,
      };
    },
    sortItemsByPositionIndex(items) {
      return items.sort((a, b) => a.position - b.position);
    },
    isSpanishPostalCode(input) {
      let residence_country = this.inputDataList.find(
        (input) => input.name == "residence_country"
      );
      // If scanned document or selected address gave us ESP as country, allow postal code to change ccca/province
      return (
        input.name == "postal_code" &&
        ((residence_country &&
          residence_country?.value == this.spanishLanguageIso3) ||
          this.guestData?.residence_country == this.spanishLanguageIso3 ||
          this.addressData?.country == this.spanishLanguageIso3) &&
        input.value.length >= 2
      );
    },
    manageAddress(address) {
      if (!address?.street) {
        return null;
      }
      let str =
        [address?.street, address?.postal_code, address?.province]
          .filter((item) => item)
          .join(", ") || null;

      return str;
    },
    sanetizeDifferencesLog(differences) {
      let items = {};
      differences.forEach((item) => {
        items[item[0].name] = {
          oldValue: item[0].value,
          newValue: item[1].value,
        };
      });
      return items;
    },
    normalizeRegionName(regionName) {
      const regionMap = {
        Catalunya: "Cataluña",
      };
      return regionMap[regionName] || regionName;
    },
  },
};
</script>

<style lang="scss" scoped>
.button {
  @apply font-black py-2 px-4 rounded mt-8 text-white uppercase;
  background-color: var(--bgColor);
  width: auto;
  &:hover {
    background-color: var(--darkenBgColor);
  }
}

.btn-wrapper > button {
  @apply w-32 md:w-40;
}
</style>
