<template>
  <div class="main-content main-content-white">
    <modal @closeModal="closeModal($event)" name="email">
      <p>
        {{ $t("senddocuments.emailValidationFailed") }}
      </p>
    </modal>
    <modal @closeModal="closeModal($event)" name="maxAttemps">
      <p>
        {{ $t("senddocuments.maximumAttemptsExceeded") }}
      </p>
    </modal>
    <div class="content h-full flex flex-col">
      <title-component>{{ $t("senddocuments.title") }}</title-component>
      <form-input
        ref="email"
        class="flex-grow"
        :name="$t('senddocuments.inputTitle')"
        :placeholder="$t('senddocuments.inputPlaceHolder')"
        type="email"
        :optional="true"
        :value="email"
        @inputChanged="inputChange($event)"
      />
      <btn
        data-test="continue-button"
        @click.native="validateEmail"
        :disabled="disableButton"
        >{{ $t("senddocuments.button") }}</btn
      >
    </div>
  </div>
</template>

<script>
import startLoading from "@/mixins/startLoading";
import stopLoading from "@/mixins/stopLoading";
import titleComponent from "@/components/shared/title.component";
import btn from "@/components/shared/button.component";
import formInput from "@/components/search/formInput.component";
import modal from "@/components/shared/modal.component";
import { mapActions, mapState, mapGetters } from "vuex";
import repository from "../repository/repositoryFactory";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import redirect from "@/mixins/redirect";
import { Tracker } from "@/tracker";

const apiEmail = repository.get("email");

export default {
  name: "SendDocuments",

  data() {
    return {
      email: "",
      subject: null,
      disableButton: false
    };
  },

  mixins: [
    startLoading,
    stopLoading,
    browserHistoryManagement,
    redirect
  ],

  components: {
    titleComponent,
    btn,
    formInput,
    modal
  },

  computed: {
    ...mapState("guest", {
      emailValidationAttempt: "emailValidationAttempt"
    }),
    ...mapState("reservations", ["reservationSelected"]),
    ...mapGetters("guest", { guestData: "getSelectedGuest", guestLiteData: "getLiteSelectedGuestData" }),
    ...mapGetters("app", ["isReceptionMode", "isDemoMode"]),
    ...mapState("brand", [
      "brandId",
      "name",
      "backgroundImage_md",
      "logo",
      "config"
    ])
  },

  async created() {
    console.log("🚀 SendDocuments created() ejecutándose");
    console.log("🔍 Config separate_documents_email:", this.config.separate_documents_email);

    // this must be before any awaits for the component to render with its value
    // If separate_documents_email is enabled, use documentsEmail if available, otherwise use main email
    this.email = this.config.separate_documents_email
      ? (this.guestData.documentsEmail || this.guestData.email || "")
      : (this.guestData.email || "");
    
    if (
      this.config.max_attempts_email_validation <= this.emailValidationAttempt
    ) {
      return await this.finishDocumentsProcess("Comments");
    }

    // When the pax is a child, it isn't required that sendDocuments view is displayed
    if (this.guestData.pax_type == "CH" && !this.config.children_sign_documents) {
      return await this.finishDocumentsProcess("Confirmation");
    }

    // If we have the email in reception mode we send the documents automatically
    if (this.email && this.isReceptionMode) {
      return await this.validateEmail();
    }

    await this.stopLoading();
  },

  methods: {
    ...mapActions("modal", ["VISIBLE", "SET_TITLE", "SET_NAME"]),

    ...mapActions("guest", ["ADD_EMAIL_VALIDATION_ATTEMPT", "UPDATE_GUEST", "UPDATE_DOCUMENTS_EMAIL"]),

    ...mapActions("loading", ["LOADING"]),

    inputChange($event) {
      this.disableButton = $event.error;
      this.email = $event.value;
    },

    async validateEmail() {
      if (this.email) {
        try {
          this.LOADING(true);
          const emailValidation = await this.checkEmail(this.email);

          if (emailValidation.valid) {
            console.log("🔍 DEBUG SendDocuments validateEmail:");
            console.log("  - separate_documents_email config:", this.config.separate_documents_email);
            console.log("  - email introducido:", this.email);
            console.log("  - email actual del guest:", this.guestData.email);
            console.log("  - documentsEmail actual del guest:", this.guestData.documentsEmail);

            // If separate_documents_email is enabled, save to documentsEmail instead of main email
            if (this.config.separate_documents_email) {
              console.log("  - Guardando en documentsEmail (separado)");
              await this.UPDATE_DOCUMENTS_EMAIL(this.email);
            } else {
              console.log("  - Guardando en email principal (comportamiento original)");
              await this.UPDATE_GUEST({ email: this.email });
            }

            if (this.isReceptionMode) {
              return await this.finishDocumentsProcess("Confirmation");
            }

            return await this.finishDocumentsProcess("PhoneForm");
          } else {
            console.error(`Email is not valid: ${this.email}`);
            await this.$refs.email.emailValidation(emailValidation);
            this.showModalError();
          }
        } catch (error) {
          console.error("Send documents error", error);
          this.showModalError();
        }
      } else {
        await this.UPDATE_GUEST({ sendDocuments: false });
        
        if (this.isReceptionMode) {
          return await this.finishDocumentsProcess("Confirmation");
        }

        return await this.finishDocumentsProcess("PhoneForm");
      }
    },

    async checkEmail() {
      try {
        return await apiEmail.validateEmail(this.email, this.brandId);
      } catch (error) {
        console.error(`Error validating email: ${this.email}`, error);
        this.showModalError();
      }
    },

    async showModalError() {
      this.ADD_EMAIL_VALIDATION_ATTEMPT();
      if (
        this.config.max_attempts_email_validation <= this.emailValidationAttempt
      ) {
        await this.SET_TITLE(this.$t("senddocuments.maximumAttemptsTitle"));
        await this.SET_NAME("maxAttemps");
      } else {
        await this.SET_TITLE(this.$t("senddocuments.emailModalTitle"));
        await this.SET_NAME("email");
      }
      await this.VISIBLE(true);
      this.stopLoading();
    },
    
    closeModal(modalName) {
      if (modalName === "maxAttemps") {
        return this.finishDocumentsProcess("PhoneForm");
      }
    },

    finishDocumentsProcess(route) {
      Tracker.recordCustomEvent("guest_documents_completed", {
        guest: this.guestLiteData,
      });

      return this.redirect({ name: route });
    }
  }
};
</script>
