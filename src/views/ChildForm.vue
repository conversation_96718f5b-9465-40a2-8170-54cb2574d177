<template>
  <div class="birthday main-content main-content-white">
    <modal name="childFormWarning" :button="false">
      <p
        v-html="
          $t('childform.modalMessage', {
            hotelName: hotel_name,
            maxAgeToNotRequireID: config.child_required_identity_documents_age,
            age: calculateChildAge
          })
        "
      ></p>
      <div class="btn-wrapper flex mt-8 justify-between">
        <btn
          data-test="closeModal"
          @click.native="VISIBLE(false)"
          class="w-32 md:w-40"
        >
          {{ $t("shared.no") }}
        </btn>
        <btn
          data-test="goToCompleteDataManually"
          @click.native="goToCompleteData"
          class="bg-white w-32 md:w-40 hover:bg-gray-50 border"
          textColor="black"
        >
          {{ $t("shared.yes") }}
        </btn>
      </div>
    </modal>
    <modal name="childFormError">
      <p
        v-html="$t('childform.modalErrorMessage', { paxType: showPaxInfo })"
      ></p>
    </modal>
    <div class="content h-full flex flex-col">
      <title-component>{{ $t("childform.title") }}</title-component>
      <div
        :key="input.name"
        v-for="input in child_inputs"
        :class="{ 'md:flex flex-wrap': input.name === 'address' && input.active === 'true' }"
      >
        <form-input
          v-if="input.name === 'address' && input.active === 'true'"
          :ref="'street_number'"
          :key="`street_number_${streetNumber}`"
          class="mb-6 md:w-2/5 md:pr-4"
          :name="$t(`validatedata.street_number`)"
          :optional="true"
          :type="'text'"
          :inputName="'street_number'"
          :minLength="'1'"
          :maxLength="'20'"
          :value="streetNumber"
          @inputChanged="setStreetNumber($event)"
          :browserAutocomplete="false"
          :autocompleteData="{ name: 'street_number', autocomplete: 'off' }"
        />

        <form-input
          :ref="input.name"
          @inputChanged="inputChanged($event, input)"
          @autocompleteSelect="autocompleteSelect($event, input)"
          :key="input.index"
          :active="input.active == 'true'"
          :optional="input.required === 'false'"
          :name="$t(`childform.${input.name}InputTitle`)"
          :type="input.name === 'address' ? 'autocomplete' : input.type"
          :inputName="`child-${input.name}-input`"
          :check-on-start="
            isReceptionMode && input.required === 'true' ? true : false
          "
          :value="input.value"
          :options="input.name === 'address' ? input.options || [] : getInputOptions(input)"
          :minLength="input.minLength"
          :maxLength="input.maxLength"
          :countryCode="input.countryCode"
          :allowsFreeText="input.name === 'address'"
          :class="{
            'mb-6': true,
            'md:w-3/5': input.name === 'address'
          }"
          disableFutureDates
          placeholder="..."
        />
      </div>

      <div class="mb-4 flex-1 flex flex-row items-start">
        <information class="info-icon flex-shrink-0" />
        <p class="ml-2 text-gray-500">
          {{ $t("childform.info") }}
        </p>
      </div>
      <btn
        @click.native="validateChildData"
        :disabled="!invalidFormButton"
        data-test="child-data-submit"
      >
        {{ $t("childform.button") }}
      </btn>
      <button data-test="go-back" class="go-back-button" @click="goBack">
        {{ $t("shared.previousStep") }}
      </button>
    </div>
  </div>
</template>
<script>
import formInput from "@/components/search/formInput.component";
import btn from "@/components/shared/button.component";
import moment from "moment";
import titleComponent from "@/components/shared/title.component";
import information from "@/assets/images/icons/informacion.svg";
import validateInputs from "@/mixins/validateInputs";
import guestConfig from "@/mixins/guestConfig";
import modal from "@/components/shared/modal.component";
import { mapActions, mapMutations, mapState, mapGetters } from "vuex";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import stopLoading from "../mixins/stopLoading";
import { cloneDeep } from "lodash";
import { countries } from "@/utils/countries";
import childsManagement from "@/mixins/childsManagement";
import redirect from "@/mixins/redirect";
import { EMITTED_VALUE } from "@/mixins/dateFormat.js"
import { Tracker } from "@/tracker";
import { Geo } from "@aws-amplify/geo";
import { debounce } from 'lodash';

export default {
  name: "ChildForm",
  data() {
    return {
      birthday: "",
      child_inputs: null,
      name: null,
      surname: null,
      nationalityValue: null,
      kinship: null,
      postalCode: null,
      address: {
        CCAA: "",
        street: "",
        street_number: "",
        city: "",
        postal_code: "",
        province: "",
        region: "",
        subregion: "",
        country: "",
      },
      municipality: null,
      email: null,
      telephone: {
        value: "",
        dialCode: "",
        countryCode: "",
      },
      invalidFormButton: false,
      nationalitySuggestions: countries,
      selected_country: null,
      streetNumber: null,
    };
  },

  mixins: [
    browserHistoryManagement,
    stopLoading,
    validateInputs,
    redirect,
    childsManagement,
    guestConfig
  ],

  components: {
    formInput,
    btn,
    titleComponent,
    information,
    modal
  },

  computed: {
    ...mapState("reservations", ["childAgeAttempts", "reservationSelected"]),
    ...mapGetters("app", ["isReceptionMode"]),
    ...mapState("brand", {
      config: "config",
      hotel_name: "name",
      brandId: "brandId",
      country: "country"
    }),
    ...mapGetters("guest", { guestData: "getSelectedGuest", guestLiteData: "getLiteSelectedGuestData", getHolderGuest: "getHolderGuest" }),

    calculateChildAge() {
      const checkInDate = this.reservationSelected.check_in;
      return moment(checkInDate).diff(moment(this.birthday), "years");
    },

    showPaxInfo() {
      if (this.guestData.pax_type === "BB") {
        return this.$t("statusComponent.BB");
      }
      return this.$t("statusComponent.CH");
    },
  },

  

  created() {
  // Deep clone the child form configuration
  this.child_inputs = cloneDeep(this.config.identification.child_form[0]);

  // Check if in reception mode and fill form with child data
  if (this.isReceptionMode) {
    this.child_inputs = this.fillFormWithChildData(this.child_inputs, this.guestData, true);
  }
  const guestHolder = this.getHolderGuest;
  if (guestHolder) {
    if (guestHolder?.address?.country) {
      this.selected_country = guestHolder.address.country;
    }
    this.child_inputs.forEach(input => {
      if (!input.value && input.fill_from_holder === 'true' && !(input.not_fill_on_reception === "true" && this.isReceptionMode)
      ) {
        if(input.name === "postal_code" && guestHolder?.address?.postal_code) {
          input.value = guestHolder.address.postal_code;
        }  
        if(input.name === "address" && guestHolder?.address?.street) {
            // Check string in street
            const streetValue = typeof guestHolder.address.street === 'object' 
              ? guestHolder.address.street.street 
              : guestHolder.address.street;
            
          this.address.street = streetValue;
          this.streetNumber = guestHolder.address.street_number || '';
          input.value = streetValue;

          this.$nextTick(() => {
            const refList = this.$refs[input.name];
            const componentRef = Array.isArray(refList) ? refList[0] : refList;

            if (componentRef?.$refs?.component?.setResult) {
              componentRef.$refs.component.setResult({
                name: streetValue,
                value: streetValue
              });
            }
          });
          this.inputChanged(
            { value: streetValue, error: false },
            input
          );
        }
        else if (input.name === "municipality" && guestHolder?.address?.city) {
          input.value = guestHolder.address.city;   
        }
          else if (input.name === "email" && guestHolder?.email) {
          input.value = guestHolder.email;
        } else if (input.name === "telephone" && guestHolder?.telephone) {
          if (typeof guestHolder.telephone === 'object') {
            input.value = guestHolder.telephone.value;
            input.countryCode = guestHolder.telephone.countryCode;
            this.telephone = {
              value: guestHolder.telephone.value,
              dialCode: guestHolder.telephone.dialCode || "+1",
              countryCode: guestHolder.telephone.countryCode
            };
          } else {
            input.value = guestHolder.telephone;
            this.telephone = {
              value: guestHolder.telephone,
              dialCode: "+1",
              countryCode: guestHolder?.address?.country || ""
            };
          }
        }
      }
    });
  }
  Tracker.recordCustomEvent("guest_form_start", {
    guest: this.guestLiteData
  });
    this.child_inputs = this.child_inputs.map(input => {
      if (input.name === 'address') {
        input.type = 'autocomplete';
        input.options = [];
      }
      return input;
    });
  },

  mounted() {
    this.stopLoading();
  },
  beforeCreate() {
    this.debouncedManageAddressInfo = debounce(async (input, searchValue) => {
      try {
        if (
          !this.config?.disable_address_autocomplete && 
          !this.config?.disable_only_address_autocomplete &&
          searchValue.length > 5
          ) {
          const suggestions = await Geo.searchForSuggestions(searchValue);

          if (input.value === searchValue) {
            const newOptions = suggestions.map((option) => ({
              name: option.text,
              value: option.text
            }));

            input.options = newOptions;

            this.$nextTick(() => {
              const refList = this.$refs[input.name];
              const componentRef = Array.isArray(refList) ? refList[0] : refList;
              if (componentRef?.$refs?.component) {
                componentRef.$refs.component.results = newOptions;
                componentRef.$refs.component.isOpen = true;
              }
            });
          }
        }
      } catch (error) {
        console.error('Error fetching address suggestions:', error);
        input.options = [];
      }
    }, 300);
  },
  methods: {
    ...mapMutations("reservations", [
      "addChildAgeAttempt",
      "resetChildAgeAttempts"
    ]),
    ...mapActions("guest", ["UPDATE_GUEST", "CLEAR_SELECTED_GUESTS"]),
    ...mapActions("loading", ["LOADING"]),
    ...mapActions("modal", ["VISIBLE", "SET_TYPE", "SET_NAME"]),
    ...mapActions("reservations", ["ADD_SESSION_GUEST"]),
    ...mapActions("queryParams", {
      SET_QUERY_PARAM: "SET_DATA",
      REMOVE_QUERY_PARAM: "REMOVE_QUERY_PARAM"
    }),

    getInputOptions(input) {
      if (input.name === "kinship") {
        return input.options?.map(option => {
          return {
            name: this.$t(`childform.kinshipOptions.${option}`),
            value: option
          };
        });
      }
      return input.options;
    },
    updateInputValue(inputName, value) {
      this.$nextTick(() => {
        const refList = this.$refs[inputName];
        const componentRef = Array.isArray(refList) ? refList[0] : refList;
        if (componentRef?.$refs?.component) {
          componentRef.$refs.component.inputValue = value;
          if (componentRef.$refs.component.setResult) {
            componentRef.$refs.component.setResult({
              name: value,
              value: value
            });
          }
        }
      });
    },
    inputChanged($event, input) {
      const INPUTS_MAP = {
        name: () => (this.name = $event.value),
        surname: () => (this.surname = $event.value),
        birthday: () => (this.birthday = $event.value),
        nationality: input => this.nationalityChanged($event, input),
        kinship: () => (this.kinship = $event.value),
        postal_code: () => {
          this.postalCode = $event.value;
          this.updateInputValue('postal_code', $event.value);
        },
        address: () => {
          this.updateInputValue('address', $event.value);
          this.address.street = $event.value;
          if (typeof $event.value === "string" && $event.value.length > 5) {
            this.debouncedManageAddressInfo(input, $event.value);
          } else {
            input.options = [];
            const refList = this.$refs[input.name];
            const componentRef = Array.isArray(refList) ? refList[0] : refList;
            if (componentRef?.$refs?.component) {
              componentRef.$refs.component.results = [];
              componentRef.$refs.component.isOpen = false;
            }
          }
        },
        CCAA: () => (this.CCAA = $event.value),
        municipality: () => {
          this.municipality = $event.value;
          this.updateInputValue('municipality', $event.value);
        },
        email: () => {
          this.email = $event.value;
          input.value = $event.value;
        },
        telephone: () => {
          if ($event.value) {
            this.telephone = {
              value: $event.value?.value || $event.value,
              dialCode: $event?.dialCode || this.telephone.dialCode,
              countryCode: $event?.country || this.telephone.countryCode,
            };
          }
        },
      };
      
      if (INPUTS_MAP[input.name]) {
        INPUTS_MAP[input.name](input);
      }
      input.hasError = $event.error;
      input.value = $event.value;

      this.validateInputs(this.child_inputs);
    },
    nationalityChanged($event, input) {
      input.options = this.nationalitySuggestions.filter(country =>
        country.name.toLowerCase().includes($event.value.toLowerCase())
      );
    },

    async autocompleteSelect($event, input) {
      input.hasError = $event.error;
      
      switch (input.name) {
        case "nationality":
          this.nationalityValue = $event.value;
          input.value = $event.value;
          break;
        case "address":
          const isAwsGeoSelection = $event.selectedOption?.placeId || 
                                  ($event.selectedOption && $event.value.includes(','));
          
          if (isAwsGeoSelection) {
            const fullAddress = $event.value;
            const displayAddress = fullAddress.split(',')[0].trim();
            
            // Get full location data from AWS Geo
            const [location] = await Geo.searchByText(fullAddress);
            if (!location) return;
            
            // Actualizar solo la calle en el input de dirección
            this.updateInputValue('address', location.street || displayAddress);
            input.value = location.street || displayAddress;
            
            // Update street number if available
            this.streetNumber = location.addressNumber || "";
            
            // Update address object
            this.address = {
              street: location.street || displayAddress,
              street_number: location.addressNumber || "",
              city: location.municipality || "",
              postal_code: location.postalCode || "",
              province: location.country === "ESP" ? location.region : "",
              region: location.country !== "ESP" ? location.region : "",
              subregion: location.country !== "ESP" ? location.subRegion : "",
              country: location.country || "",
              CCAA: location.country === "ESP" ? location.region : "",
            };

            // Update related fields
            if (location.postalCode) {
              const postalCodeInput = this.child_inputs.find(i => i.name === "postal_code");
              if (postalCodeInput) {
                this.inputChanged({ value: location.postalCode, error: false }, postalCodeInput);
              }
            }

            if (location.municipality) {
              const municipalityInput = this.child_inputs.find(i => i.name === "municipality");
              if (municipalityInput) {
                this.inputChanged({ value: location.municipality, error: false }, municipalityInput);
              }
            }
          }
          break;
        default:
          input.value = $event.value;
      }
      
      input.options = [];
      this.validateInputs(this.child_inputs);
    },

    updateRelatedAddressInputs(location) {
      this.child_inputs.forEach((input) => {
        switch (input.name) {
          case "postal_code":
            input.value = location.postalCode;
            break;
          case "municipality":
            input.value = location.municipality;
            break;
          case "region":
            input.value = location.region;
            break;
          case "subregion":
            input.value = location.subRegion;
            break;
        }
      });
    },

    async finishChildrenCheckin(
      birthday,
      name = null,
      surname = null,
      nationalityValue = null,
      kinship = null
    ) {
      const guestHolder = this.getHolderGuest;
      const childData = {
        pax_type: this.guestData.pax_type,
        birthday_date: birthday
      };

      // Add other basic information if present
      if (name) childData.name = name;
      if (surname) childData.surname = surname;
      if (nationalityValue) childData.nationality = nationalityValue;
      if (kinship) childData.kinship = kinship;

      childData.address = {
        CCAA: (this.address.country || guestHolder?.address?.country) === "ESP" 
          ? (this.address.CCAA || guestHolder?.address?.CCAA || "") 
          : "",
        city: this.address.city || guestHolder?.address?.city || "",
        country: this.address.country || guestHolder?.address?.country || "",
        postal_code: this.address.postal_code || guestHolder?.address?.postal_code || "",
        province: this.address.province || guestHolder?.address?.province || "",
        region: this.address.region || guestHolder?.address?.region || "",
        street: this.address.street || guestHolder?.address?.street || "",
        street_number: this.streetNumber || guestHolder?.address?.street_number || "",
        subregion: this.address.subregion || guestHolder?.address?.subregion || "",
      };

      const emailInput = this.child_inputs.find(i => i.name === 'email');
      childData.email = emailInput?.value || 
                        this.email || 
                        (emailInput?.fill_from_holder === 'true' ? guestHolder?.email : '') || 
                        "";
      // Handle telephone
      if (this.telephone.value) {
        childData.telephone = {
          ...this.telephone
        };
      } else if (guestHolder?.telephone) {
        childData.telephone = {
          ...guestHolder.telephone
        };
      } else {
        childData.telephone = {
          value: '',
          dialCode: "+1",
          countryCode: ""
        };
      }

      await this.UPDATE_GUEST(childData);

      Tracker.recordCustomEvent("guest_form_completed", {
        guest: childData
      });
      return this.redirect({ name: "Confirmation" });
    },

    async validateChildData() {
      const maxChildAgeAttempts = this.config.max_attempts_child;
      const maxAgeToNotRequireID = this.config
        .child_required_identity_documents_age;
      const checkInDate = this.reservationSelected.check_in;
      const ADULT_AGE = this.getAdultAge()

      const childRequiresDocument = this.childRequiresDocument(
        this.birthday,
        checkInDate,
        maxAgeToNotRequireID
      );

      let childAge = moment(checkInDate).diff(moment(this.birthday), "years");
      console.info("Child birthdate inserted", {
        childRequiresDocument,
        paxType: this.guestData.pax_type,
        birthday: moment(this.birthday).format(EMITTED_VALUE),
        checkInDate: moment(checkInDate).format(EMITTED_VALUE),
        age: childAge,
        maxAgeToNotRequireID
      });

      this.addChildAgeAttempt();
      if (
        this.childAgeAttempts > maxChildAgeAttempts &&
        childRequiresDocument
      ) {
        this.resetChildAgeAttempts();
        return this.redirect({
          name: "Error",
          params: {
            error: "ageError",
            route: "Status"
          }
        });
      } else if (
        this.childAgeAttempts <= maxChildAgeAttempts &&
        childRequiresDocument &&
        childAge < ADULT_AGE
      ) {
        await this.VISIBLE(true);
        await this.SET_TYPE("info");
        await this.SET_NAME("childFormWarning");
      } else if (childAge >= ADULT_AGE) {
        await this.VISIBLE(true);
        await this.SET_TYPE("error");
        await this.SET_NAME("childFormError");
      } else {
        this.resetChildAgeAttempts();
        await this.finishChildrenCheckin(
          this.birthday,
          this.name,
          this.surname,
          this.nationalityValue,
          this.kinship
        );
      }
    },
    async goToCompleteData() {
      this.SET_QUERY_PARAM({ manualProcess: true });
      await this.VISIBLE(false);
      await this.LOADING(true);
      return this.redirect({
        name: "Scan"
      });
    },
    async goBack() {
			await this.CLEAR_SELECTED_GUESTS();
			return this.redirect({ name: "Status" });
		},
    beforeDestroy() {
      if (this.debouncedManageAddressInfo) {
        this.debouncedManageAddressInfo.cancel();
      }
    },
    setStreetNumber(value) {
      this.streetNumber = value.value;
    },
  }
};
</script>
<style lang="scss" scoped>
.main-color-link {
  color: var(--bgColor);
}
.info-icon {
  width: 30px;
  height: 30px;
}
</style>

