<template>
  <div class="main-content main-content-white h-full">
    <modal name="emailValidation">{{
      $t("senddocuments.emailValidationFailed")
    }}</modal>

    <modal name="holderData" :button="false">
      <p>
        {{ $t("receptionvalidatedata.modal.body") }}
      </p>
      <div class="btn-wrapper flex mt-8 justify-between ">
        <btn
          class="bg-white hover:bg-gray-50 border"
          textColor="black"
          @click.native="closeModal"
          data-test="use-guest-data"
        >
          {{ $t("shared.no") }}
        </btn>
        <btn @click.native="handleUseHolderData" data-test="use-holder-data">
          {{ $t("shared.yes") }}
        </btn>
      </div>
    </modal>

    <div class="content flex flex-col h-full">
      <title-component>{{ $t("receptionvalidatedata.title") }}</title-component>
      <form
        class="h-full flex flex-col justify-between "
        @submit.prevent="onSubmit"
      >
      <H1>holaaaaaaaaaaa</H1>
        <div class="flex flex-col">
          <EmailInput
            :ref="formData.email.name"
            @inputChanged="inputChanged"
            class="mb-6"
            :active="formData.email.active"
            :optional="!formData.email.required"
            :name="$t(`validatedata.${formData.email.name}`)"
            :type="formData.email.type"
            v-model="formData.email.value"
            inputName="email"
            :label="formData.email.label"
            :minLength="formData.email.minLength"
            :maxLength="formData.email.maxLength"
            placeholder="..."
            :disabled="!formData.email.active"
          />

          <Checkbox
            :name="$t(`validatedata.${formData.send_documents.name}`)"
            type="checkbox"
            inputName="send_documents"
            :label="formData.send_documents.label"
            :disabled="!formData.send_documents.active"
            v-model="formData.send_documents.value"
            @inputChanged="inputChanged"
          />

          <PhoneInput
            :ref="formData.telephone.name"
            @inputChanged="inputChanged"
            class="mb-6"
            :active="formData.telephone.active"
            :optional="!formData.telephone.required"
            :name="$t(`validatedata.${formData.telephone.name}`)"
            :type="formData.telephone.type"
            v-model="formData.telephone.value"
            inputName="telephone"
            :label="formData.telephone.label"
            :minLength="formData.telephone.minLength"
            :maxLength="formData.telephone.maxLength"
            placeholder="..."
            :disabled="!formData.telephone.active"
            :countryCode="countryCode"
          />

          <TextInput
            :ref="formData.postal_code.name"
            @inputChanged="inputChanged"
            class="mb-6"
            :active="formData.postal_code.active"
            :optional="!formData.postal_code.required"
            :name="$t(`validatedata.${formData.postal_code.name}`)"
            :type="formData.postal_code.type"
            v-model="formData.postal_code.value"
            inputName="postal_code"
            :label="formData.postal_code.label"
            :minLength="formData.postal_code.minLength"
            :maxLength="formData.postal_code.maxLength"
            placeholder="..."
            :disabled="!formData.postal_code.active"
          />
        </div>

        <btn class="mt-4" :disabled="!this.checkSubmitForm" type="Submit">{{
          $t("validatedata.button")
        }}</btn>
      </form>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions, mapGetters } from "vuex";
import EmailInput from "@/components/search/inputs/reception/emailInput.component";
import Checkbox from "@/components/search/inputs/checkboxInput.component";
import PhoneInput from "@/components/search/inputs/reception/phoneInput.component";
import TextInput from "@/components/search/inputs/reception/defaultInput.component";
import redirect from "@/mixins/redirect";
import stopLoading from "@/mixins/stopLoading";
import startLoading from "@/mixins/startLoading";
import emailValidation from "@/mixins/emailValidation";
import sendDocuments from "@/mixins/sendDocuments";
import titleComponent from "@/components/shared/title.component";
import modal from "@/components/shared/modal.component";
import btn from "@/components/shared/button.component";

export default {
  name: "ReceptionValidateData",
  components: {
    btn,
    titleComponent,
    modal,
    EmailInput,
    Checkbox,
    PhoneInput,
    TextInput
  },
  mixins: [stopLoading, startLoading, redirect, emailValidation, sendDocuments],
  async created() {
    // For now this inputs are hardcoded
    this.formData = {
      email: {
        name: "email",
        type: "email",
        active: true,
        required: true,
        minLength: "0",
        maxLength: "40",
        value: this.guestData?.email ?? "",
        hasError: false
      },
      send_documents: {
        name: "send_documents",
        type: "checkbox",
        active: true,
        required: false,
        value: false,
        hasError: false,
        label: this.$t("receptionvalidatedata.checkboxText")
      },
      telephone: {
        name: "telephone",
        type: "phone",
        active: true,
        required: true,
        minLength: "0",
        maxLength: "10",
        value: this.guestData?.telephone ?? "",
        hasError: false
      },
      postal_code: {
        name: "postal_code",
        type: "text",
        active: true,
        required: true,
        minLength: "0",
        maxLength: "10",
        value: this.guestData?.address?.postal_code ?? "",
        hasError: false
      }
    };

    this.countryCode = this.guestData.nationality || "ES";

    await this.stopLoading();

    if (this.isHolderGuestCompleted && !this.guestData.holder) {
      return await this.showModal({
        type: "info",
        name: "holderData",
        title: this.$t("receptionvalidatedata.modal.title")
      });
    }
  },

  computed: {
    ...mapGetters("guest", {
      guestData: "getSelectedGuest",
      getHolderGuest: "getHolderGuest",
      isHolderGuestCompleted: "isHolderGuestCompleted"
    }),
    ...mapState("brand", [
      "brandId",
      "name",
      "backgroundImage_md",
      "logo",
      "config"
    ]),
    checkSubmitForm() {
      return Object.values(this.formData).every(input => {
        if (input.required) {
          return input.value && !input.hasError;
        }
        return !input.hasError;
      });
    }
  },

  data() {
    return {
      formData: {},
      countryCode: "ES"
    };
  },

  methods: {
    ...mapActions("guest", ["UPDATE_GUEST"]),
    ...mapActions("modal", ["VISIBLE", "SET_TITLE", "SET_NAME", "SET_TYPE"]),
    ...mapActions("loading", ["LOADING"]),
    async onSubmit() {
      await this.LOADING(true);

      // Email validation
      if (this.formData.email.value) {
        try {
          let isValidEmail = await this.validateEmail(
            this.formData.email.value,
            this.brandId
          );
          if (!isValidEmail) {
            await this.$refs.email[0]?.$refs.component.emailValidation({
              valid: false
            });
            throw new Error("Email validation error");
          }
        } catch (error) {
          // Show error modal
          console.error({ error });
          await this.showModal({
            type: "error",
            name: "emailValidation",
            title: this.$t("senddocuments.emailModalTitle")
          });
          return await this.LOADING(false);
        }

        // Save data to user
        await this.UPDATE_GUEST({
          ...this.guestData,
          address: {
            ...this.guestData.address,
            postal_code: this.formData.postal_code.value
          },
          telephone: {
            value: this.formData.telephone.value
          },
          email: this.formData.email.value
        });
        // Send email documents
        if (
          this.formData.send_documents.value ||
          this.config.send_signed_documents_to_reception
        ) {
          try {
            await this.sendDocuments(this.formData.send_documents.value);
          } catch (error) {
            console.error("Send documents error");
          }
        }

        return this.redirect({ name: "Confirmation" });
      }
    },
    async inputChanged(input) {
      this.formData[input.name] = {
        ...this.formData[input.name],
        value: input.value,
        hasError: input.error
      };
    },
    async showModal(data) {
      await this.SET_TYPE(data.type);
      await this.SET_NAME(data.name);
      await this.SET_TITLE(data.title);
      await this.VISIBLE(true);
    },

    async handleUseHolderData() {
      this.formData.email.value =
        this.getHolderGuest.email ?? this.formData.email.value;
      this.formData.telephone.value =
        this.getHolderGuest.telephone ?? this.formData.telephone.value;
      this.formData.postal_code.value =
        this.getHolderGuest.address?.postal_code ??
        this.formData.postal_code.value;

      await this.closeModal();
    },

    async closeModal() {
      await this.VISIBLE(false);
    }
  }
};
</script>
<style lang="scss" scoped>
.btn-wrapper > button {
  @apply w-32 md:w-40;
}
</style>
