<template>
  <div class="confirmation main-content main-content-white" 
  :class="{'opacity-0': isLoading}"
  >
    <modal
      v-if="this.config.show_modal_in_confirmation_page"
      data-test="confirmModal"
      name="finishedCheckin"
      :button="false"
    >
      <CheckinCounter />
      <p
        v-if="!this.config.partial_checkin"
        v-html="$t('confirmation.modalMessageNotPartial')"
      ></p>
      <p
        v-if="this.config.partial_checkin"
        v-html="$t('confirmation.modalMessage')"
      ></p>
      <btn
        data-test="closeModalAndStartNewCheckin"
        @click.native="closeModalAndStartNewCheckin"
        class="my-4"
      >
        {{ $t("confirmation.modalButtonContinue") }}
      </btn>
      <btn
        v-if="!this.config.partial_checkin"
        data-test="exit-button"
        @click.native="showModalExitProcess"
        textColor="black"
        class="bg-white hover:bg-gray-50 border"
      >
        {{ $t("confirmation.modalButtonExit") }}
      </btn>
      <btn
        v-if="this.config.partial_checkin"
        data-test="closeModal"
        @click.native="VISIBLE(false)"
        textColor="black"
        class="bg-white hover:bg-gray-50 border"
      >
        {{ $t("confirmation.modalButtonExit") }}
      </btn>
    </modal>
    <modal name="exitApp" :button="false" class="text-black" data-test="exit">
      <p>{{ $t("header.modalContent") }}</p>
      <div class="btn-wrapper flex mt-8 justify-between">
        <btn @click.native="closeModal" data-test="close-modal">
        {{
        $t("shared.no")
        }}</btn>
        <btn
          class="bg-white hover:bg-gray-50 border"
          textColor="black"
          @click.native="exit"
          data-test="exit-app"
        >
          {{ $t("shared.yes") }}
        </btn>
      </div>
    </modal>
    <div class="content h-full flex flex-col justify-between">
      <div class="flex flex-col items-center">
        <tick class="m-auto" />
        <p class="font-bold text-2xl text-center my-4 text-green-200" data-test="confirmationCheckText">
          {{ $t("confirmation.checkinFinished") }}</p>
      </div>
      <div :class="{ 'flex-grow': this.config.show_qr_code }">
        <RedirectLink
          data-test="redirect-link"
          v-if="redirectLinkAvailable"
          :link="redirectLink"
          :hotelData="hotelData"
        />
        <div v-if="this.config.show_qr_code">
          <div class="qr-code flex justify-center" v-show="!errorExist">
            <qr
              data-test="qr-code"
              class="border-2 border-gray-300 p-2 rounded"
              id="qrcode"
              :value="qrValue"
              renderAs="svg"
              :size="size"
              level="H"
            />
            <canvas id="qrcanvas" width="800" height="800" />
          </div>
          <p class="font-black text-3xl text-center mt-4">{{ codeQr }}</p>
          <div v-if="richConfirmationText">
            <p
              data-test="richConfirmationText"
              class="my-4"
              v-html="richConfirmationText">
            </p>
          </div>
          <div v-else>
            <p v-show="qrValue.length>1" data-test="defaultConfirmationText" class="text-center my-4">
              {{ $t("confirmation.text") }}
            </p>
          </div>
        </div>
        <div v-if="!this.config.show_qr_code && richConfirmationText">
          <p
            data-test="richConfirmationText"
            class="my-4"
            v-html="richConfirmationText">
          </p>
        </div>
      </div>
      <section class="flex flex-col" v-show="!errorExist" data-test="btn-section">
        <btn
          v-if="
            (guestData.pax_type == 'AD' ||
              guestList.some(guest => guest.full_name)) &&
              this.config.show_qr_code"
          @click.native="createPDF"
        >
          {{ $t("confirmation.createPDF") }}
        </btn>
        <btn
          v-if="
            (this.config.partial_checkin || this.multipleRooms) &&
              !this.getReservationCompleted() && !this.isReceptionMode && 
              (this.checkState !== 'pre_checked_in' && this.checkState !== 'check_in')"
          @click.native="startNewCheckin"
          class="mt-4"
          data-test="nextGuest"
        >
          {{ $t("confirmation.startNewCheckin") }}
        </btn>
        <btn class="mt-4" 
          v-if="this.config.partial_checkin && !this.getReservationCompleted() && !this.isReceptionMode"
          @click.native="shareButtonRedirect"
          data-test="shareButton"
        >
          {{ $t("confirmation.shareButton") }}
        </btn>
      </section>
    </div>
      <title-component
        class="text-green-200"
        v-if="showMessageIfButtonAvailable()"
        data-test="closeWindowMessage"
      >
        {{ this.getReservationCompleted() ? $t('confirmation.closeWindowFinishedBooking') : $t('confirmation.closeWindowOnPartial') }}
      </title-component>
  </div>
</template>
<script>
import qr from "qrcode.vue";
import { jsPDF } from "jspdf";
import tick from "@/assets/images/icons/tickCheckIn.svg";
import Canvg from "canvg";
import moment from "moment";
import titleComponent from "@/components/shared/title.component";
import { mapActions, mapGetters, mapState } from "vuex";
import repository from "../repository/repositoryFactory";
import startLoading from "@/mixins/startLoading";
import stopLoading from "@/mixins/stopLoading";
import clearStoreAndRedirect from "@/mixins/clearStoreAndRedirect";
import btn from "@/components/shared/button.component";
import redirect from "@/mixins/redirect";
import RedirectLink from "@/components/confirmation/RedirectLink.component";
import modal from "@/components/shared/modal.component";
import CheckinCounter from "../components/confirmation/CheckinCounter.component";
import { getGuestAgeOnCheckin } from "@/utils/guestUtils";
import { cloneDeep } from "lodash";
import { Tracker } from "@/tracker";
import setDocumentVariablesValues from "@/mixins/setDocumentVariablesValues";

const api = repository.get("integration");
const AutoCheckinApi = repository.get("checkin");

export default {
  name: "confirmation",
  components: {
    titleComponent,
    qr,
    btn,
    RedirectLink,
    modal,
    CheckinCounter,
    tick
  },
  mixins: [startLoading, stopLoading, redirect, clearStoreAndRedirect, setDocumentVariablesValues],
  data() {
    return {
      richConfirmationText: "",
      confirmationTextType: 2,
      qrValue: "",
      codeQr: null,
      size: 200,
      redirectLinkAvailable: false,
      redirectLink: "",
      buttonVisible: false,
      errorExist: false,
      baseSubject: null, 
    };
  },
  computed: {
    ...mapState("brand", ["config", "name", "brandId", "backgroundImage_sm", "backgroundImage_md",
      "logo"]),
    ...mapState("reservations", [
      "reservationSelected",
      "reservationCheckinComplete",
      "dataRequested",
      "multipleRooms",
      "data",
      "checkState"
    ]),
    ...mapState("scan", ["face"]),
    ...mapState("guest", { guestList: "list" }),
    ...mapGetters("guest", { guestData: "getSelectedGuest", guestLiteData: "getLiteSelectedGuestData" }),
    ...mapGetters("app", ["isReceptionMode","isDemoMode"]),
    ...mapState("loading", ["loading"]),
    ...mapGetters("reservations", ["getReservationCompleted", "getError"]),
    ...mapGetters("guest", ["getGuestsCheckinComplete", "getFirstGuest", "getGuestsToSynchronize"]),
    ...mapGetters("brand", ["getActiveProducts"]),
    ...mapState("modal", { modalName: "name" }),
    isLoading() {
      return this.$store.state.loading.loading;
    },

    hotelData() {
      return {
        name: this.name,
        background: this.backgroundImage_sm
      };
    },

    allRequiredGuestsHaveSigned() {
      return this.guestList.every(guest => {
        if (guest.signedDocuments) {
          return true;
        }
        if(this.config.children_process_on_reception){
          return false;
        }

        const isAdult = guest.pax_type === "AD";

        const isMinor = getGuestAgeOnCheckin(
          this.reservationSelected.check_in,
          guest?.birthday_date
        ) < this.config.child_required_identity_documents_age;
        
        const scanLikeAdult = this.config.scan_children_like_adults
        if(!isAdult && this.config.scan_children_like_adults){
          return false
        }
        

        return (
          !isAdult && isMinor && !scanLikeAdult
        );
      });
    },

    isRedirectAvailable() {
      return (
        this.config.redirect_link &&
        this.getActiveProducts.includes("portal_redirect")
      );
    },
    isDemoCheckin() {
      return process.env.VUE_APP_BRANDS_DEMO?.split(",")?.includes(
        this.brandId
      );
    }
  },
  async created() {
    // If we reach confirmation page and theres no reservation selected, throw Error
    try{
      if (!this.reservationSelected) {
        throw this.getError
      }
    }catch(error){
      const alreadyOnPrecheckErrorCode = "INT_3_2";
      if(error && error.code === alreadyOnPrecheckErrorCode){ //If we found a reservation with no data and status on alreadyPrecheck we stay in Confirmation
        this.errorExist = true
        if (this.config.custom_confirmation_text) {
          await this.getCustomConfirmationText();
        }
        return this.stopLoading()
      } else {
        return this.redirect({
            name: "Error"
          });
      }
    }
    // Portal redirect
    if (
      this.config.redirect_link &&
      this.getActiveProducts.includes("portal_redirect")
    ) {
      try {
        const redirectData = await api.getRedirectUrl(this.brandId, {
          checkIn: this.reservationSelected.check_in,
          checkOut: this.reservationSelected.check_out,
          locator: this.reservationSelected.res_localizer,
          roomNumber: this.reservationSelected.res_room_number,
          resId: this.reservationSelected.res_id,
          email: this.guestData.email,
          firstName: this.guestData.name,
          language: this.$i18n.locale
        });
        if (redirectData.data?.url) {
          this.redirectLink = redirectData.data?.url[0];

          this.redirectLinkAvailable = true;
        }
      } catch (error) {
        console.error("getRedirectUrl error", { error });
      }
    }

    if (
      !this.reservationCheckinComplete &&
      Object.keys(this.guestData).length !== 0
    ) {
      if (this.isDemoCheckin) {
        this.getGuestsToSynchronize.forEach((guest) => {
          guest.validated = true;
        });
      }
      
      this.getGuestsToSynchronize.forEach((guest) => {
        guest.processCompleted =  true, 
        guest.signedDocuments = true
        guest.mustBeSynchronized = false;
        this.ADD_SESSION_GUEST();
      });
    }
    if (this.config.custom_confirmation_text) {
      await this.getCustomConfirmationText();
    }
    if (this.config.show_qr_code) {
      await this.createQrCode();
    }
    if (!this.reservationCheckinComplete) {
      Tracker.recordCustomEvent("guest_completed", {
        guest: this.guestLiteData
      });
      // Check if all guest have checkinComplete
      if (this.config.partial_checkin || (this.getReservationCompleted(true) && !this.isReceptionMode) || (this.allRequiredGuestsHaveSigned && this.isReceptionMode) )  {
        try {
          let bodyRequest = {
            res_id: this.reservationSelected.res_id,
            check_in: this.reservationSelected.check_in,
            check_out: this.reservationSelected.check_out,
            guests: this.config.partial_checkin
              ? this.getGuestsToSynchronize.map(guest => this.getFormattedGuest(guest))
              : this.guestList.map(guest => this.getFormattedGuest(guest)),
            reception: this.isReceptionMode ? true : false
          };

            // Check if the value of the `province` property is 0, then change it to null
          if (bodyRequest.guests[0].address.province === "0") {
            bodyRequest.guests[0].address.province = null;
          }
          // Check if the value of the `state` property is 0, then change it to null
          if (bodyRequest.guests[0].address.state === "0") {
            bodyRequest.guests[0].address.state = null;
          }

          console.info("Send guest to PMS", {
            brandId: this.brandId,
            payload: bodyRequest
          });

          const response = await api.sendGuestToPMS(this.brandId, bodyRequest);
          // Integrations could return a 200 with errors (Third party errors behave like that)
          // So if payload returns errors, thrown an Error
          if (response?.error) {
            throw new Error();
          }
          if ((this.getReservationCompleted(true) && !this.isReceptionMode) || (this.allRequiredGuestsHaveSigned && this.isReceptionMode)) {
            Tracker.recordCustomEvent("reservation_completed", {
              reservation: this.sanitizeReservation(this.reservationSelected)
            });
          }

          this.SET_RESERVATION_CHECKIN_COMPLETE(true);
          if (this.config.send_signed_documents_to_reception || !this.config.disable_send_documents_page){
            this.sendDocuments() 
          }       
        } catch (error) {
          console.error("sendGuestToPMS error", { error });
          const errorCode = error.response?.data?.error?.code;
          const alreadyOnPrecheckErrorCode = "INT_3_2"; // When reservation is already on pre-checkin state

          
          if (errorCode === alreadyOnPrecheckErrorCode) {
            return  this.stopLoading();
          }
          return this.redirect({
            name: "Error"
          });
        }

        if (this.isReceptionMode) {
          if (!this.allRequiredGuestsHaveSigned) {
            await this.SET_RESERVATION_CHECKIN_COMPLETE(false);
            return this.redirect({ name: "Status" });
          }else{
            await this.SET_RESERVATION_CHECKIN_COMPLETE(true)
          }
        }
        
        this.stopLoading();
      } else {
        if (!this.config.show_modal_in_confirmation_page) {
          this.clearScanData();
          return this.redirect({ name: "Status" });
        }
        this.stopLoading()
      }
    }
    this.stopLoading();

    // Checks if all guests have done check-in
    if (!this.guestList.every(guest => guest.processCompleted)) {
      await this.showModal();
    }
  },

  watch: {
  "$i18n.locale": async function() {
    await this.getCustomConfirmationText()
    this.stopLoading()
    }
  },
  
  methods: {
    ...mapActions("guest", ["ADD_GUEST_TO_LIST", "CLEAR_DATA", "UPDATE_GUEST"]),
    ...mapActions("reservations", [
      "SET_RESERVATION_CHECKIN_COMPLETE",
      "ADD_SESSION_GUEST",
      "SET_RESERVATION_SELECTED"
    ]),
    ...mapActions({
      clearGuestData: "guest/CLEAR_STATE",
      clearScanData: "scan/CLEAR_STATE",
      clearReservationData: "reservations/CLEAR_STATE"
    }),
    ...mapActions("loading", ["LOADING"]),
    ...mapActions("modal", [
      "VISIBLE",
      "SET_TITLE",
      "SET_NAME",
      "SET_TYPE",
      "CLEAR_STATE"
    ]),
    ...mapActions("brand", ["SET_BRAND_ID"]),

    async getCustomConfirmationText() {
      this.richConfirmationText = await AutoCheckinApi.getCustomizedText(
        this.brandId,
        this.confirmationTextType,
        this.$i18n.locale
      )
        .then(response => {
         return response.data[0]?.translations[0]?.text || null
        })
        .catch(async error => {
          console.error("Error getting confirmation text", { error });
          return null;
        });

      if (this.richConfirmationText) {
        // Replace template variables with retrieved values
        const variables = this.setDocumentVariablesValues(this.guestData);
        Object.entries(variables).forEach(entry => {
          const [variable, value] = entry;
          this.richConfirmationText = this.richConfirmationText.replaceAll(
            `{{${variable}}}`,
            value
          );
        });
      }
    },
    async getEmailConfiguration() {
      try{
        const {data: emailData} = await AutoCheckinApi.getEmailConfiguration(this.brandId);   
        const documentsRequestedEmails = emailData.filter(item => item.action_name === 'autocheckin_documents_requested')
        this.baseSubject= documentsRequestedEmails[0]?.subject
      } catch(error) {
        console.error("Get subject error", error)
      }
    },

    replaceSubjectVariables(baseSubject, variables) {
      let updatedSubject = baseSubject;
      for (const [key, value] of Object.entries(variables)) {
        updatedSubject = updatedSubject?.replace(`{{${key}}}`, value);
      }
      return updatedSubject;
    },

    getEmailForDocuments(guest) {
      // If separate_documents_email is enabled, use documentsEmail if available, otherwise use main email
      if (this.config.separate_documents_email) {
        return guest.documentsEmail || guest.email;
      }
      return guest.email;
    },

    async sendDocuments() {
        let guestsToSend = [this.guestData]
        if ((!this.config.partial_checkin)){
          guestsToSend = this.guestList.filter(guest => 
            guest.documentsSaved 
          );
        }

        if (this.config.send_signed_documents_to_reception){
          await this.getEmailConfiguration();
        }

        return Promise.all(guestsToSend.map(async guest => {
          let subject = null;
          if (this.config.send_signed_documents_to_reception && this.baseSubject) {
            const documentVariablesValues = this.setDocumentVariablesValues(guest);
            subject = this.replaceSubjectVariables(this.baseSubject, documentVariablesValues); 
          }
          
          const body = {
            documents: guest.documentsSaved,
            identityDocuments: null,
            brand: {
              id: this.brandId,
              name: this.name,
              background: this.backgroundImage_md,
              logo: this.logo,
              demoMode: this.isDemoMode,
              sendSignedDocumentsToReception: this.config.send_signed_documents_to_reception
            },
            user: {
              name: guest.name,
              gender: guest.gender,
              email: this.config.disable_send_documents_page || !guest.sendDocuments? null : this.getEmailForDocuments(guest), //handle cases where the email is not sent to the guest, either due to brand config or the guest's decision in sendDocuments view
              lang: this.$i18n.locale
            },
            subject
          };

          if (this.config.send_identity_documents_to_reception) {
            body.identityDocuments = guest.identityDocumentsSaved;
          }

          Tracker.recordCustomEvent("guest_documents_sent", {
            guest: guest,
          });

          return AutoCheckinApi
            .sendDocuments(body)
            .catch(error => console.error("Send documents ERROR", { error }));
        }));
    },

    async createQrCode() {
      this.qrValue = `
        ${
          this.reservationSelected.res_localizer
            ? `${this.$t("confirmation.localizer")}: ${
                this.reservationSelected.res_localizer
              }`
            : ""
        }
        \u000A${this.$t("confirmation.reservation")}: ${
        this.reservationSelected.res_id
      }
        \u000A${this.$t("confirmation.name")}: ${this.guestData.full_name}
        \u000A${this.$t("confirmation.checkIn")}: ${
        this.reservationSelected.check_in
      }
        \u000A${this.$t("confirmation.checkOut")}: ${
        this.reservationSelected.check_out
      }
        \u000A${this.$t("confirmation.pax")}: ${parseInt(
        this.reservationSelected.res_adults
      ) + parseInt(this.reservationSelected.res_children)}
        \u000A${this.$t("confirmation.nights")}: ${
        this.reservationSelected.res_nights
      }`;

      this.codeQr = this.reservationSelected.res_id;
    },

    async createPDF() {
      let svg = document.getElementById("qrcode").innerHTML;
      let canvas = document.getElementById("qrcanvas");
      let ctx = canvas.getContext("2d");
      let v = await Canvg.fromString(ctx, svg);
      await v.render();
      let imgData = canvas.toDataURL("image/png");
      const doc = new jsPDF({
        format: "a6",
        unit: "mm",
        orientation: "p"
      });
      doc.text(this.name, 52, 12, { align: "center" });
      doc.setFontSize(12);
      doc.text(this.$t("confirmation.text"), 52, 20, {
        align: "center"
      });

      doc.setFontSize(10);
      doc.setTextColor(128, 128, 128);
      doc.text(this.$t("confirmation.reservation"), 10, 35);

      doc.setFontSize(12);
      doc.setTextColor(0, 0, 0);
      doc.text(
        this.guestData?.pax_type == "CH" || this.guestData?.pax_type == "BB"
          ? this.guestList?.find(
              guest =>
                (guest.full_name && guest.pax_type != "CH") ||
                (guest.full_name && guest.pax_type != "BB")
            )?.full_name
          : this.guestData?.full_name || this.getFirstGuest?.full_name || "",
        10,
        40
      );

      if (this.reservationSelected.res_localizer) {
        doc.setFontSize(10);
        doc.setTextColor(128, 128, 128);
        doc.text(this.$t("confirmation.localizer"), 95, 35, "right");

        doc.setFontSize(12);
        doc.setTextColor(0, 0, 0);
        doc.text(this.reservationSelected.res_localizer, 95, 40, "right");
      }

      doc.setFontSize(10);
      doc.setTextColor(128, 128, 128);
      doc.text(this.$t("confirmation.checkIn"), 95, 50, "right");
      doc.setTextColor(0, 0, 0);
      doc.text(this.reservationSelected.check_in, 95, 55, "right");
      doc.setTextColor(128, 128, 128);
      doc.text(this.$t("confirmation.checkOut"), 95, 62, "right");
      doc.setTextColor(0, 0, 0);
      doc.text(this.reservationSelected.check_out, 95, 68, "right");
      doc.setTextColor(128, 128, 128);
      doc.text(this.$t("confirmation.nights"), 95, 76, "right");
      doc.setTextColor(0, 0, 0);
      doc.text(this.reservationSelected.res_nights.toString(), 95, 81, "right");
      doc.setTextColor(128, 128, 128);
      doc.text(this.$t("confirmation.pax"), 95, 89, "right");
      doc.setTextColor(0, 0, 0);
      doc.text(
        this.reservationSelected.guests.length.toString(),
        95,
        94,
        "right"
      );
      doc.addImage(imgData, "PNG", 10, 47, 50, 50);
      doc.setFontSize(14);
      doc.text(this.reservationSelected.res_id, 22, 103);
      let filename = `qr-code-${this.reservationSelected.res_localizer}.pdf`;

      // Check if is iOS device to open in new tab to download document
      // see full explanation in https://hotelinking.atlassian.net/wiki/spaces/MA/pages/2234318856/Descarga+de+documentos
      if (navigator.platform.match(/iPhone|iPod|iPad/)) {
        window.open(
          doc.output("bloburi", {
            filename
          }),
          "_blank"
        );
      } else {
        doc.save(filename);
      }
    },
    checkGuestsHaveCompletedCheckin() {
      const reservationAdultsAmount = this.reservationSelected.res_adults;
      const reservationJuniorsAmount = this.reservationSelected.res_juniors;
      const reservationchildrenAmount = this.reservationSelected.res_children;
      const reservationBabiesAmount = this.reservationSelected.res_babies;

      const adultsHaveCompletedCheckinAmount = this.reservationSelected.guests.filter(
        guest => guest.pax_type === "AD"
      );
      const juniorHaveCompletedCheckinAmount = this.reservationSelected.guests.filter(
        guest => guest.pax_type === "JR"
      );
      const childrenHaveCompletedCheckinAmount = this.reservationSelected.guests.filter(
        guest => guest.pax_type === "CH"
      );
      const babiesHaveCompletedCheckinAmount = this.reservationSelected.guests.filter(
        guest => guest.pax_type === "BB"
      );

      return (
        adultsHaveCompletedCheckinAmount === reservationAdultsAmount &&
        juniorHaveCompletedCheckinAmount === reservationJuniorsAmount &&
        childrenHaveCompletedCheckinAmount === reservationchildrenAmount &&
        babiesHaveCompletedCheckinAmount === reservationBabiesAmount
      );
    },

    getFormattedGuest(guest) {
        let objectGuest = {
        pms_id: guest.pms_id || null,
        position: guest.position || null,
        ...(guest.selected ? { face: this.face || null } : {}),
        validated:
          typeof guest.validated === "boolean" && this.config.partial_checkin
            ? guest.validated
            : null,
        pax_type: guest.pax_type || null,
        holder: guest.holder || null,
        name: guest.name || null,
        kinship: guest.kinship || null,
        surname: guest.surname || null,
        second_surname: guest.second_surname || null,
        full_name: guest.full_name || null,
        nationality: guest.nationality || null,
        phone: this.getFormattedPhone(guest?.telephone),
        email: this.config.separate_documents_email ? null : (guest.email || null),
        language: this.getLanguage(window.navigator.userLanguage || window.navigator.language),
        identity_document: {
          type: guest.document_type,
          number: guest.document_number || null,
          document_support_number: guest.document_support_number,
          fiscal_code: guest.fiscal_code,
          expedition_date: guest.date_of_issue
            ? moment(guest.date_of_issue, true).format("YYYY-MM-DD")
            : null,
          expiration_date: guest.date_of_expiry
            ? moment(guest.date_of_expiry, true).format("YYYY-MM-DD")
            : null
        },
        address: {
          city: guest.address?.city || null,
          street: guest.address?.street || null,
          postal_code: guest.address?.postal_code || null,
          province: guest.address?.province || null,
          state: guest.address?.CCAA || null,
          region: guest.address?.region || null,
          subregion: guest.address?.subregion || null,
          country: guest.residence_country || null
        },
        comment: {
          comment: guest?.comment?.comment || null,
          hours: isNaN(guest?.comment?.hours)
            ? null
            : parseInt(guest?.comment?.hours),
          minutes: isNaN(guest?.comment?.hours)
            ? null
            : parseInt(guest?.comment?.minutes)
          //This isNaN is because if the user sends 00 as hours or minutes, JS interprets it as false, therefore it defaults to null, doing it this way we check if it is a numeric value and if so, we parse it to int and send it
        },
        birthday:
          guest.birthday_date || guest.birthday
            ? moment(guest.birthday_date || guest.birthday, true).format(
                "YYYY-MM-DD"
              )
            : null,
        data_protect: true, // Defaults to true if not configured
        terms_conditions: true, // Defaults to true if not configured
        commercial_data: true, // Defaults to true if not configured
        commercial_data_share: null,
      };

      // Override default checkbox states if configured
      objectGuest.data_protect = guest.checkboxStates?.data_protect ?? objectGuest.data_protect;
      objectGuest.terms_conditions = guest.checkboxStates?.terms_conditions ?? objectGuest.terms_conditions;
      objectGuest.commercial_data = guest.checkboxStates?.commercial_data ?? objectGuest.commercial_data;
      objectGuest.commercial_data_share =  guest.checkboxStates?.commercial_data_share ?? objectGuest.commercial_data_share;

      if (!guest.gender) {
        objectGuest.gender = null;
      } else if (["male", "female"].includes(guest.gender)) {
        objectGuest.gender = guest.gender;
      } else {
        objectGuest.gender =
          guest.gender === "M"
            ? "male"
            : guest.gender === "F"
            ? "female"
            : null;
      }

      // Append timestamp coordinates signature string
      objectGuest.timestamp_signature = guest.timestampSignature || null;

      // Apend the base64 image of the user signature
      objectGuest.signature = guest.documentSignature || null;

      // Append signed documents
      if (guest.documents && guest.documentsSaved) {
        objectGuest.documents = guest.documentsSaved.files.map(file => {
          return {
            name: file.fileName.substr(0, file.fileName.lastIndexOf(".")) || file.fileName,
            path: `${guest.documentsSaved.key}/${file.fileName}`,
            documentCount: file.documentCount,
            documentId: file.documentId
          };
        });
      } else {
        objectGuest.documents = [];
      }

      // Append identity documents
      if (guest.identityDocumentsSaved) {
        objectGuest.documents = [
          ...objectGuest.documents,
          ...guest.identityDocumentsSaved.files.map(file => {
            return {
              name: file.name.substr(0, file.name.lastIndexOf(".")) || file.name,
              path: `${file.key}/${file.name}`
            };
          })
        ];
      }
      
      // Concatenate house_number with street
      if (guest.address?.street) {
          objectGuest.address.street = [
            guest.address?.street, guest.address?.street_number
          ]
					.filter((item) => item)
					.join(", ") || ""
      }

      return objectGuest;
    },

    getFormattedPhone(phone) {
      if (phone && phone?.dialCode && phone?.value) {
        const dialCode = phone.dialCode.replace("undefined", "");
        const telephone = phone.value.replace(phone.dialCode, "");
        return (dialCode + telephone).replace(/\++/g, "+");
      }

      return null;
    },

    getLanguage(locale) {
      try {
        return new Intl.Locale(locale)?.language;
      } catch (e) {
        console.warn("Error parsing locale of user");
        return null;
      }
    },

    async startNewCheckin() {
      await this.LOADING(true);
      this.SET_RESERVATION_CHECKIN_COMPLETE(false);
      const reservation = [
        {
          name: "reservation_code",
          value: this.reservationSelected.res_localizer
        }
      ];
      this.clearScanData();

      if (this.isDemoCheckin) {
        return await this.redirect({ name: "Status" });
      }

      this.clearGuestData();
      this.clearReservationData();
      
      return await this.redirect({
        name: "Search",
        params: {
          reservation: [reservation]
        }
      });
    },

    async shareButtonRedirect() {
      await this.LOADING(true);
      this.redirect({ name: "Share" });
    },


    async showModal() {
      let title;

      if (this.guestData.name && this.guestData.surname) {
        title = this.$t("confirmation.modalTitle", {
          guestName: this.guestData.name,
          guestSurname: this.guestData.surname
        });
      } else {
        title = this.$t("confirmation.modalTitleNoName");
      }

      await this.SET_TYPE("success");
      await this.SET_NAME("finishedCheckin");
      await this.SET_TITLE(title);
      await this.VISIBLE(true);
    },

    async showModalExitProcess() {
      await this.SET_TYPE("error");
      await this.SET_NAME("exitApp");
      await this.SET_TITLE(this.$t("header.modalTitle"));
      await this.VISIBLE(true);
    },

    async closeModal() {
      await this.CLEAR_STATE();
      await this.VISIBLE(false);
      await this.showModal();
    },

    async closeModalAndStartNewCheckin() {
      await this.CLEAR_STATE();
      await this.VISIBLE(false);

      if (!this.config.partial_checkin) {
        await this.clearScanData();
        await this.LOADING(true);
        await this.SET_RESERVATION_CHECKIN_COMPLETE(false);
        return await this.redirect({ name: "Status" });
      }
      await this.startNewCheckin();
    },

    async exit() {
      this.clearStoreAndRedirect();
    },

    showMessageIfButtonAvailable() {
      return (
        (this.config.show_qr_code ||
        (this.config.partial_checkin || this.multipleRooms) &&
        !this.getReservationCompleted() ||
        (this.config.partial_checkin && !this.getReservationCompleted())) 
        && !this.errorExist
      );
    },
    sanitizeReservation(reservation) {
      const guests = this.guestList.map((guest) => {
        return {
          name: guest.name,
          surname: guest.surname,
          second_surname: guest.second_surname,
          nationality: guest.nationality,
        };
      });

      const sanitizedReservation = cloneDeep(reservation);
      sanitizedReservation.guests = guests;

      return sanitizedReservation;
    }
  }
};
</script>
<style lang="scss">
#qrcanvas {
  position: fixed;
  left: -3000px;
  top: -3000px;
}
.btn-wrapper > button {
  @apply w-32 md:w-40;
}
</style>
