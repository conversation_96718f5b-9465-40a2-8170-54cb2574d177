<template>
  <div class="main-content main-content-white">
    <div class="content h-full flex flex-col">
      <modal name="failedChildData">
        {{ $t("childsdata.error", { index: failedIndex }) }}
      </modal>
      <title-component>{{ $t("childsdata.title") }}</title-component>
      <p class="text-gray-800 mb-4 search-text">
        {{ $t("childsdata.text") }}
      </p>
      <div class="flex-1">
        <tabs
          v-if="tabNames.length > 1"
          :itemsNames="tabNames"
          @tabToggle="onTabClicked"
        />
        <div
          v-for="(inputs, keyGroup) in formInputs"
          :key="keyGroup"
        >
          <div
            v-if="currentGroup === keyGroup"
            v-for="(input, index) in inputs"
            :key="index"
            :class="{ 'md:flex flex-wrap': input.name === 'address' }"
          >
            <form-input
              v-if="input.name === 'address' && input.active === 'true'"
              :ref="'street_number'"
              :key="`street_number_${keyGroup}_${index}`"
              class="mb-6 md:w-2/5 md:pr-4"
              :name="$t(`validatedata.street_number`)"
              :optional="true"
              :type="'text'"
              :inputName="'street_number'"
              :minLength="'1'"
              :maxLength="'20'"
              :value="streetNumbers[keyGroup] || ''"
              @inputChanged="setStreetNumber($event, keyGroup)"
              :browserAutocomplete="false"
              :autocompleteData="{ name: 'street_number', autocomplete: 'off' }"
              :disabled="isChildValidated(keyGroup) && !isReceptionMode"
            />

            <form-input
              :ref="input.name"
              @inputChanged="handleInputChanged($event, input)"
              @autocompleteSelect="autocompleteSelect($event, input)"
              :key="input.index"
              :active="input.active == 'true'"
              :optional="input.required === 'false'"
              :name="$t(`childform.${input.name}InputTitle`)"
              :type="input.name === 'address' ? 'autocomplete' : input.type"
              :inputName="`child-${input.name}-input`"
              :check-on-start="Boolean(input.value)"
              :countryCode="input.countryCode"
              :value="typeof input.value === 'object' ? input.value.value : input.value"
              :options="getInputOptions(input)"
              :minLength="input.minLength"
              :maxLength="input.maxLength"
              :disabled="input.disabled"
              :allowsFreeText="input.name === 'address'"
              disableFutureDates
              :class="{
                'mb-6': true,
                'md:w-3/5': input.name === 'address',
              }"
              placeholder="..."
            />
          </div>
        </div>
      </div>
      <btn
        @click.native="submit()" 
        :disabled="!submitAvailable"
        data-test="submitButton"
      >
        {{ $t("validatedata.button") }}
      </btn>
    </div>
  </div>
</template>
<script>
import modal from "@/components/shared/modal.component";
import tabs from "@/components/tabs/tabs.vue";
import startLoading from "../mixins/startLoading";
import stopLoading from "../mixins/stopLoading";
import { mapActions, mapGetters, mapState } from "vuex";
import titleComponent from "@/components/shared/title.component";
import formInput from "@/components/search/formInput.component";
import btn from "@/components/shared/button.component";
import { cloneDeep, debounce } from "lodash";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import redirect from "@/mixins/redirect";
import childsManagement from "@/mixins/childsManagement";
import { countries } from "@/utils/countries";
import guestConfig from "@/mixins/guestConfig";
import moment from "moment";
import { Geo } from "@aws-amplify/geo";

export default {
  name: "ChildsData",
  components: { titleComponent, formInput, btn, tabs, modal },
  data() {
    return {
      currentGroup: 0,
      tabNames: [],
      formInputs: [],
      failedIndex: null,
      nationalitySuggestions: countries,
      streetNumbers: {},
    };
  },
  mixins: [startLoading, stopLoading, browserHistoryManagement, redirect, childsManagement, guestConfig],
  computed: {
    ...mapState("brand", ["brand", "config"]),
    ...mapGetters("guest", ["getChildGuests", "getHolderGuest"]),
    ...mapGetters("app", ["isReceptionMode"]),
    ...mapState("reservations", ["reservationSelected"]),
    submitAvailable() {
      return this.isFormDataCompleted(this.formInputs)
    },
    // Helper to check if a child is validated
    isChildValidated() {
      return (childIndex) => {
        return this.getChildGuests[childIndex]?.validated || false;
      };
    },
  },

  beforeCreate() {
    this.debouncedManageAddressInfo = debounce(async (input, searchValue) => {
      try {
        if (
          !this.config?.disable_address_autocomplete && 
          !this.config?.disable_only_address_autocomplete &&
          searchValue.length > 5 
        ) {
          const suggestions = await Geo.searchForSuggestions(searchValue);

          if (input.value === searchValue) {
            const newOptions = suggestions.map((option) => ({
              name: option.text,
              value: option.text
            }));

            input.options = newOptions;

            this.$nextTick(() => {
              const refList = this.$refs[input.name];
              const componentRef = Array.isArray(refList) ? refList[0] : refList;

              if (componentRef?.$refs?.component) {
                componentRef.$refs.component.results = newOptions;
                componentRef.$refs.component.isOpen = true;
              }
            });
          }
        }
      } catch (error) {
        console.error('Error fetching address suggestions:', error);
        input.options = [];
      }
    }, 300);
  },

  async created() {
    this.getChildGuests.forEach((childData, index) => {
      this.tabNames.push(this.$t("childsdata.minor", { index: index + 1 }));

      const childInputs = cloneDeep(this.config.identification.child_form[0]);
      const childInputsFilled = this.fillFormWithChildData(childInputs, childData, this.isReceptionMode);

      // Initialize street number for this child
      this.streetNumbers[index] = childData?.address?.street_number || null;

      // Prefill inputs with holder data if available and input is empty (BEFORE changing to autocomplete)
      // Skip prefill for validated guests since their inputs are disabled
      if (!childData.validated || this.isReceptionMode) {
        this.prefillWithHolderData(childInputsFilled, index);
      }

      // Set address inputs as autocomplete type (AFTER prefilling)
      childInputsFilled.forEach(input => {
        if (input.name === 'address') {
          input.type = 'autocomplete';
          input.options = [];
        }
      });

      this.formInputs.push(childInputsFilled);

    });

    if (this.isFormDataCompleted(this.formInputs) && !this.isReceptionMode) {
      return this.redirect({ name: "Documents" });
    }

    this.stopLoading();
  },

  mounted() {
    // Ensure address fields are properly updated in the UI after component is mounted
    // Skip for validated guests since their inputs are disabled
    this.$nextTick(() => {
      this.formInputs.forEach((formInputs, childIndex) => {
        const childData = this.getChildGuests[childIndex];
        if (!childData?.validated) {
          const addressInput = formInputs.find(input => input.name === "address");
          if (addressInput && addressInput.value && !addressInput.disabled) {
            this.updateInputValue('address', addressInput.value);
          }
        }
      });
    });
  },

  methods: {
    ...mapActions("modal", ["VISIBLE", "SET_NAME"]),

    prefillWithHolderData(inputs, childIndex) {
      const guestHolder = this.getHolderGuest;
      if (!guestHolder) return;

      inputs.forEach(input => {
        // Only prefill if input is empty, has fill_from_holder flag, and is not disabled
        if (!input.value && input.fill_from_holder === 'true' && !input.disabled &&  !(input.not_fill_on_reception === "true" && this.isReceptionMode)

      ) {
          switch (input.name) {
            case "postal_code":
              if (guestHolder?.address?.postal_code) {
                input.value = guestHolder.address.postal_code;
              }
              break;
            case "address":
              if (guestHolder?.address?.street) {
                const streetValue = typeof guestHolder.address.street === 'object'
                  ? guestHolder.address.street.street
                  : guestHolder.address.street;
                input.value = streetValue;

                // Also update the UI component after Vue has rendered
                this.updateInputValue('address', streetValue);
              }
              // Also prefill street number if available and not already set
              if (guestHolder?.address?.street_number && !this.streetNumbers[childIndex]) {
                this.streetNumbers[childIndex] = guestHolder.address.street_number;
              }
              break;
            case "municipality":
              if (guestHolder?.address?.city) {
                input.value = guestHolder.address.city;
              }
              break;
            case "email":
              if (guestHolder?.email) {
                input.value = guestHolder.email;
              }
              break;
            case "telephone":
              if (guestHolder?.telephone) {
                if (typeof guestHolder.telephone === 'object') {
                  input.value = guestHolder.telephone.value;
                  input.countryCode = guestHolder.telephone.countryCode;
                } else {
                  input.value = guestHolder.telephone;
                }
              }
              break;
            case "nationality":
              if (guestHolder?.nationality) {
                input.value = guestHolder.nationality;
              }
              break;
          }
        }
      });
    },

    setStreetNumber(event, childIndex) {
      this.streetNumbers[childIndex] = event.value;
    },

    updateInputValue(inputName, value) {
      this.$nextTick(() => {
        const refList = this.$refs[inputName];
        const componentRef = Array.isArray(refList) ? refList[0] : refList;
        if (componentRef?.$refs?.component) {
          componentRef.$refs.component.inputValue = value;
          if (componentRef.$refs.component.setResult) {
            componentRef.$refs.component.setResult({
              name: value,
              value: value
            });
          }
        }
      });
    },

    getInputOptions(input) {
      if (input.name === "kinship") {
        return input.options?.map(option => {
          return {
            name: this.$t(`childform.kinshipOptions.${option}`),
            value: option
          };
        });
      }
      return input.options;
    },
    async handleInputChanged(event, input) {
      this.$set(input, 'value', event.value);
      this.$set(input, 'error', event.error);

      if (input.name === "nationality") {
        input.options = this.nationalitySuggestions.filter(country =>
          country.name.toLowerCase().includes(event.value.toLowerCase())
        );
      } else if (input.name === "address") {
        // Handle address autocomplete
        if (typeof event.value === "string" && event.value.length > 5) {
          this.debouncedManageAddressInfo(input, event.value);
        } else {
          input.options = [];
          const refList = this.$refs[input.name];
          const componentRef = Array.isArray(refList) ? refList[0] : refList;
          if (componentRef?.$refs?.component) {
            componentRef.$refs.component.results = [];
            componentRef.$refs.component.isOpen = false;
          }
        }
      } else if (input.name === "telephone") {
        input.value = event.value;
        input.dialCode = event.dialCode;
        input.countryCode = event.country;        
      }
    },
    async autocompleteSelect($event, input) {
      this.$set(input, 'value', $event.value);
      this.$set(input, 'error', $event.error);
      
      switch (input.name) {
        case "nationality":
          input.value = $event.value;
          break;
        case "address": {
          const isAwsGeoSelection = $event.selectedOption?.placeId || ($event.selectedOption && $event.value.includes(','));
          
          if (isAwsGeoSelection && $event.event) {
            const fullAddress = $event.value;
            const displayAddress = fullAddress.split(',')[0].trim();
            
            // Get full location data from AWS Geo
            const [location] = await Geo.searchByText(fullAddress);
            if (!location) return;
            
            // Actualizar solo la calle en el input de dirección
            this.updateInputValue('address', location.street || displayAddress);
            input.value = location.street || displayAddress;
            
            // Update street number if available
            this.streetNumber = location.addressNumber || "";
            
            // Update address object
            this.address = {
              street: location.street || displayAddress,
              street_number: location.addressNumber || "",
              city: location.municipality || "",
              postal_code: location.postalCode || "",
              province: location.country === "ESP" ? location.region : "",
              region: location.country !== "ESP" ? location.region : "",
              subregion: location.country !== "ESP" ? location.subRegion : "",
              country: location.country || "",
              CCAA: location.country === "ESP" ? location.region : "",
            };

            // Update related fields
            if (location.addressNumber) {
              this.streetNumbers[this.currentGroup] = location.addressNumber;
              this.updateInputValue('street_number', location.addressNumber);
            }

            if (location.postalCode) {
              const postalCodeInput = this.formInputs[this.currentGroup].find(i => i.name === "postal_code");
              if (postalCodeInput) {
                this.handleInputChanged({ value: location.postalCode, error: false }, postalCodeInput);
                this.updateInputValue('postal_code', location.postalCode);

              }
            }

            if (location.municipality) {
              const municipalityInput = this.formInputs[this.currentGroup].find(i => i.name === "municipality");
              if (municipalityInput) {
                this.handleInputChanged({ value: location.municipality, error: false }, municipalityInput);
                this.updateInputValue('municipality', location.municipality);
              }
            }
          }
        } break;
        default:
          input.value = $event.value;
      }
    },

    isFormDataCompleted(formData) {
      return formData.every(group => 
        group.filter(input => (input.active === "true" || input.active === true) && !input.disabled).every(input => (input.value || input.required === 'false') && !input.error)
      );
    },

    onTabClicked(tab) {
      this.currentGroup = tab;
    },
    
    async submit() {
      const failedChildIndex = await this.validateChilds();
      
      if (failedChildIndex !== -1) {
        this.failedIndex = failedChildIndex + 1;
        await this.SET_NAME("failedChildData");
        await this.VISIBLE(true);
        return;
      }

      this.getChildGuests.forEach((childData, index) => {
        const formInputs = this.formInputs[index];

        // Basic information
        childData.name = formInputs.find(input => input.name === "name")?.value;
        childData.surname = formInputs.find(input => input.name === "surname")?.value;
        childData.full_name = [childData?.name, childData?.surname, childData?.second_surname].filter((item) => item).join(" ") || null;
        childData.birthday_date = formInputs.find(input => input.name === "birthday")?.value;
        childData.nationality = formInputs.find(input => input.name === "nationality")?.value;
        childData.kinship = formInputs.find(input => input.name === "kinship")?.value;
        childData.mustBeSynchronized = !formInputs.find(input => input.name === "name")?.disabled;
        childData.email = formInputs.find(input => input.name === "email")?.value;
        childData.address.street = formInputs.find(input => input.name === "address")?.value;
        childData.address.street_number = this.streetNumbers[index] || null;
        childData.address.postal_code = formInputs.find(input => input.name === "postal_code")?.value;
        childData.address.city = formInputs.find(input => input.name === "municipality")?.value;

        const telephoneInput = formInputs.find(input => input.name === "telephone");
        if (telephoneInput?.value) {
          if (typeof telephoneInput.value === 'object') {
            childData.telephone = telephoneInput.value;
          } else {
            childData.telephone = {
              value: telephoneInput.value,
              dialCode: telephoneInput?.dialCode,
              countryCode: telephoneInput?.countryCode
            };
          }
        }
      });

      return this.redirect({ name: "Documents" });
    },

    async validateChilds() {
      const maxAgeToNotRequireID =
				this.config.child_required_identity_documents_age;
			const checkInDate = this.reservationSelected.check_in;
			const ADULT_AGE = this.getAdultAge();
      
      return this.formInputs.findIndex((inputs) => {
        const birthdayInput = inputs.find(input => input.name === "birthday");
        const childRequiresDocument = this.childRequiresDocument(birthdayInput.value, checkInDate, maxAgeToNotRequireID);
        const childAge = moment(checkInDate).diff(moment(birthdayInput.value), "years");


        return (childRequiresDocument || childAge >= ADULT_AGE) && !birthdayInput.disabled;
      });
    }
  }
};
</script>
