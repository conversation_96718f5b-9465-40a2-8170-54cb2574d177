import "cypress-file-upload";
import { receptionReservationWithNullDateOfIssue, receptionReservation } from "../../../mocks/modules/integration/data";
import { defaultConfig } from "../../../mocks/modules/brand/data";
import { passportScan } from "../../../mocks/modules/ocr/data";

describe("Reception full process", () => {
	const timeout = 3500;
	const getTranslations = (locale, translation) =>
		cy.window().its(`app._i18n._vm.messages.${locale}.${translation}`);

	before(() => {
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/brands/:id/reservations",
				method: "get",
			},
			receptionReservation,
			false,
		);
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/validator",
				method: "post",
				body: {
					email: "<EMAIL>",
				},
			},
			{ email_quantity: "deliverable", email_rate: 1, valid: true },
			false,
		);
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/documents/send",
				method: "post",
				body: {},
			},
			{},
			false,
		);
	});

	after(() => {
		cy.restartServiceWorkerHandlers();
	});
it("starts the e2e test on login page", () => {
		cy.startServiceWorker();
		cy.startReceptionFlow();

		const username = Cypress.env("cognitoUser");
		const password = Cypress.env("cognitoPassword");
		cy.logMessage("should login with cognito credentials");
		cy.loginByCognito(username, password);
		cy.logMessage("Login completed. Now should look for a reservation");

		cy.url({ timeout }).should("include", "search");
		cy.get('[data-test="room_number"]').type("0504");
		cy.get('[data-test="check_in"] input[type="text"]')
			.clear()
			.type("26-01-2022");
		cy.get('[data-test="searchButton"]').click();

		cy.logMessage("should choose a guest with all data validated");
		cy.wait(timeout);

		cy.url({ timeout: 1000 }).should("include", "status");
		cy.get('[data-test="guestList"]:not(:disabled)', { timeout }).eq(0).click();
		cy.logMessage("should accept conditions and move to Documents page");

		cy.url({ timeout }).should("include", "privacy");
		cy.get('[data-test="gdprText"]').should(
			(text) => expect(text).not.to.be.empty,
		);
		cy.wait(1500);
		cy.get('[data-test="acceptConditionsButton"]').click();

		cy.logMessage(
			"should click go back button and see validate data page with card displayed",
		);
		cy.url({ timeout: 4000 }).should("include", "documents");
		cy.get('[data-test="closeModal"]').filter(":visible").click();
		cy.wait(1000);
		cy.get('[data-test="goBackButton"]').click();

		cy.logMessage(
			"should click on show all button and show all inputs, then change some values",
		);
		cy.url({ timeout }).should("include", "validate-data");
		cy.get('[data-test="showAllButton"]').click();
		cy.get('[data-test="name"]').clear().type("Alex Marcelo");
		cy.get('[data-test="surname"]').clear().type("Quiroga");
		cy.get('[data-test="second_surname"]').clear().type("López");
		cy.get('[data-test="postal_code"]').clear().type("07004");
		cy.get('[data-test="validate-data-button"]').click();
		cy.get('[data-test="accept-differences-modal"]').click();

		cy.logMessage("should accept all documents then pass to the next step");
		cy.url({ timeout }).should("include", "documents");
		cy.get('[data-test="closeModal"]').filter(":visible").click();
		cy.get('[data-test="close-document-button"]').click();
		cy.wait(1000);
		cy.get('[data-test="close-document-button"]').click();

		cy.logMessage(
			"should sign documents, validate email and go to pick next guest page",
		);
		cy.url({ timeout }).should("include", "signature");
		cy.get("canvas")
			.click()
			.trigger("mousedown", { which: 1, x: 50, y: 50 })
			.trigger("mousemove", { which: 1, x: 50, y: 60 })
			.trigger("mouseup");
		cy.get('[data-test="continue-button"]').click();

		cy.logMessage("should pick next guest and go to validate-data view");
		cy.url({ timeout: 6000 }).should("include", "status");
		cy.get('[data-test="guestList"]:not(:disabled)', { timeout: 1000 })
			.eq(0)
			.click();

		cy.logMessage(
			"should click on see all button and show all inputs, then change some values",
		);
		cy.url({ timeout }).should("include", "validate-data");
		cy.get('[data-test="showAllButton"]').click();
		cy.get('[data-test="name"]').clear().type("Alex Marcelo");
		cy.get('[data-test="surname"]').clear().type("Quiroga");
		cy.get('[data-test="second_surname"]').clear().type("López");
		cy.get('[data-test="postal_code"]').clear().type("07004");
		cy.get('[data-test="validate-data-button"]').click();
		cy.get('[data-test="birthday_date"]').type("{selectall}{backspace}");

		getTranslations("es", "inputs.emptyValueError").then((translation) => {
			cy.get('[data-test="empty-value-error"]').should("contain", translation);
		});

		cy.get('[data-test="birthday_date"]').type("{selectall}{backspace}");

		cy.get('[data-test="birthday_date"]').type(
			Cypress.moment().format("DD-MM-YYYY"),
		);

		cy.get('[data-test="birthday_date_error"]').should("not.be.visible");

		cy.get('[data-test="birthday_date"]').type("{selectall}{backspace}");

		cy.get('[data-test="birthday_date"]').type("32/13/2025");

		getTranslations("es", "inputs.dateFormatError").then((translation) => {
			cy.get('[data-test="date-format-error"]').should("contain", translation);
		});

		cy.get('[data-test="birthday_date"]').type("{selectall}{backspace}");

		cy.get('[data-test="birthday_date"]').type(
			Cypress.moment().subtract(121, "years").format("DD-MM-YYYY"),
		);

		getTranslations("es", "inputs.date.tooOld").then((translation) => {
			cy.get('[data-test="birthday_date_error"]').should(
				"contain",
				translation,
			);
		});

		cy.get('[data-test="birthday_date"]').type("{selectall}{backspace}");

		cy.get('[data-test="birthday_date"]').type(
			Cypress.moment().subtract(20, "years").format("DD-MM-YYYY"),
		);

		cy.get('[data-test="birthday_date_error"]', { timeout }).should(
			"not.be.visible",
		);
		cy.get('[data-test="birthday_date"]').click();
		cy.get('[data-test="date_of_expiry"]').type("{selectall}{backspace}");

		getTranslations("es", "inputs.emptyValueError").then((translation) => {
			cy.get('[data-test="empty-value-error"]').should("contain", translation);
		});

		cy.get('[data-test="date_of_expiry"]').type("{selectall}{backspace}");
		cy.get('[data-test="date_of_expiry"]').type("32/13/2025");

		getTranslations("es", "inputs.dateFormatError").then((translation) => {
			cy.get('[data-test="date-format-error"]').should("contain", translation);
		});

		cy.get('[data-test="date_of_expiry"]').type("{selectall}{backspace}");

		cy.get('[data-test="date_of_expiry"]').type(
			Cypress.moment().subtract(121, "years").format("DD-MM-YYYY"),
		);

		getTranslations("es", "inputs.dateFormatError").then((translation) => {
			cy.get('[data-test="date_of_expiry_error"]').should(
				"contain",
				translation.replace("{name}", "fecha de caducidad"),
			);
		});

		cy.get('[data-test="date_of_expiry"]').type("{selectall}{backspace}");

		cy.get('[data-test="date_of_expiry"]').type(
			Cypress.moment().format("DD-MM-YYYY"),
		);

		cy.get('[data-test="date_of_expiry_error"]', { timeout }).should(
			"not.be.visible",
		);

		cy.get('[data-test="date_of_issue"]').type("{selectall}{backspace}");

		getTranslations("es", "inputs.emptyValueError").then((translation) => {
			cy.get('[data-test="empty-value-error"]').should("contain", translation);
		});

		cy.get('[data-test="date_of_issue"]').type("{selectall}{backspace}");

		cy.get('[data-test="date_of_issue"]').type("32/13/2025");

		getTranslations("es", "inputs.dateFormatError").then((translation) => {
			cy.get('[data-test="date-format-error"]').should("contain", translation);
		});

		cy.get('[data-test="date_of_issue"]').type("{selectall}{backspace}");

		cy.get('[data-test="date_of_issue"]').type(
			Cypress.moment().add(1, "day").format("DD-MM-YYYY"),
		);

		getTranslations("es", "inputs.date.greaterThanTodayError").then(
			(translation) => {
				cy.get('[data-test="date_of_issue_error"]').should(
					"contain",
					translation.replace("{name}", "fecha de expedición"),
				);
			},
		);

		cy.get('[data-test="date_of_issue"]').type("{selectall}{backspace}");

		cy.get('[data-test="date_of_issue"]').type(
			Cypress.moment().subtract(1, "day").format("DD-MM-YYYY"),
		);

		cy.get('[data-test="date_of_issue_error"]', { timeout }).should(
			"not.be.visible",
		);
		cy.get('[data-test="gender"]').select("male");
		cy.get('[data-test="document_number"]').clear().type("123456789A");
		cy.get('[data-test="document_number"]').clear().type("88888888L");
		cy.get('[data-test="nationality"]', { timeout }).type("A");
		cy.wait(timeout);
		cy.get('[data-test="nationality-option"]', { timeout }).first().click();
		cy.get('[data-test="document_type"]').select("passport");
		cy.get('[data-test="address"]').clear();
		cy.get('[data-test="address"]', { timeout }).type("Amiens Street, IRL", {
			delay: 50,
		});
		cy.wait(1000);

		cy.get('[data-test="address-option"]', { timeout }).first().click();
		cy.wait(1000);
		cy.get('[data-test="postal_code"]', { timeout })
			.invoke("val")
			.should("not.be.empty");
		cy.get('[data-test="validate-data-button"]').click();
		cy.get('[data-test="accept-differences-modal"]').click();

		cy.logMessage("should accept all documents then pass to the next step");
		cy.url({ timeout }).should("include", "documents");
		cy.get('[data-test="closeModal"]').filter(":visible").click();
		cy.get('[data-test="close-document-button"]').click();
		cy.wait(1000);
		cy.get('[data-test="close-document-button"]').click();

		cy.logMessage(
			"should sign documents, validate email and go to pick next guest page",
		);
		cy.url({ timeout }).should("include", "signature");
		cy.get("canvas")
			.click()
			.trigger("mousedown", { which: 1, x: 50, y: 50 })
			.trigger("mousemove", { which: 1, x: 50, y: 60 })
			.trigger("mouseup");
		cy.get('[data-test="continue-button"]').click();

		cy.logMessage("should continue without setting an email");
		cy.url({ timeout }).should("include", "send-documents");
		cy.get('[data-test="continue-button"]').click();

		cy.logMessage("should redirect to confirmation view");
		cy.url({ timeout }).should("include", "confirmation");
	});

	it("scan on reception config test", () => {
		defaultConfig.data.advanced_scan = false;

		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/brands/:id/products/:id/configuration",
				method: "get",
			},
			defaultConfig,
			false,
		);
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/brands/:id/reservations",
				method: "get",
			},
			receptionReservationWithNullDateOfIssue,
			false,
		);
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/validator",
				method: "post",
				body: {
					email: "<EMAIL>",
				},
			},
			{ email_quantity: "deliverable", email_rate: 1, valid: true },
			false,
		);
		cy.overrideEndpointResponse(
			{
				method: "post",
				status: 200,
				endpoint: "*/brands/:id/scan",
			},
			passportScan,
		);

		cy.startServiceWorker();
		cy.startReceptionFlow();

		const username = Cypress.env("cognitoUser");
		const password = Cypress.env("cognitoPassword");
		cy.loginByCognito(username, password);

		cy.url({ timeout }).should("include", "search");
		cy.get('[data-test="room_number"]').type("0504");
		cy.get('[data-test="check_in"] input[type="text"]')
			.clear()
			.type("26-01-2022");
		cy.get('[data-test="searchButton"]').click();
		cy.wait(timeout);
		cy.url({ timeout: 1000 }).should("include", "status");
		cy.get('[data-test="guestList"]:not(:disabled)', { timeout }).eq(0).click();
		cy.url({ timeout }).should("include", "privacy");
		cy.get('[data-test="gdprText"]').should(
			(text) => expect(text).not.to.be.empty,
		);
		cy.wait(1500);
		cy.get('[data-test="acceptConditionsButton"]').click();

		cy.logMessage("if scan_on_reception is activated, should go to scan page");

		cy.url({ timeout: 4000 }).should("include", "scan");

		cy.logMessage(
			"after scanning a valid document with all the information, should redirect to validate-data page",
		);

		cy.get('[data-test="fileInput"]').attachFile("goodPassport.png");
		cy.get('[data-test="fileInput"]').trigger("input", { force: true });

		cy.url({ timeout }).should("include", "validate-data");

		cy.logMessage("Validate data card should not be shown");

		cy.get('[data-test="validate-data-card"]').should("not.be.visible");
	});

	it("should navigate to scan page if guest date_of_issue is null", () => {
    defaultConfig.data.advanced_scan = false; 

		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/brands/:id/products/:id/configuration",
				method: "get",
			},
			defaultConfig,
			false,
		);
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/brands/:id/reservations",
				method: "get",
			},
			receptionReservationWithNullDateOfIssue,
			false,
		);
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/validator",
				method: "post",
				body: {
					email: "<EMAIL>",
				},
			},
			{ email_quantity: "deliverable", email_rate: 1, valid: true },
			false,
		);
		cy.overrideEndpointResponse(
			{
				method: "post",
				status: 200,
				endpoint: "*/brands/:id/scan",
			},
			passportScan,
		);

    cy.startServiceWorker();
    cy.startReceptionFlow();

    const username = Cypress.env("cognitoUser");
    const password = Cypress.env("cognitoPassword");
    cy.loginByCognito(username, password);

    cy.url({ timeout: 10000 }).should("include", "search");
    cy.get('[data-test="room_number"]').type("0504");
    cy.get('[data-test="check_in"] input[type="text"]').clear().type("26-01-2022");
    cy.get('[data-test="searchButton"]').click();
    cy.wait(1000);
    cy.url({ timeout: 10000 }).should("include", "status");
    cy.get('[data-test="guestList"]:not(:disabled)', { timeout: 10000 }).eq(0).click();
    cy.url({ timeout: 10000 }).should("include", "privacy");
    cy.get('[data-test="acceptConditionsButton"]').click();

    cy.url({ timeout: 4000 }).should("include", "scan");
});

});
