import { 
  getSuggestionFilters, 
  isIreland, 
  shouldValidateIEorUK, 
  validateIEorUKPostalCode,
  validatePostalCodeForCountry
} from "@/utils/address";
import { postcodeValidator, postcodeValidatorExistsForCountry } from "postcode-validator";

// Mock postcode-validator
jest.mock("postcode-validator", () => ({
  postcodeValidator: jest.fn(),
  postcodeValidatorExistsForCountry: jest.fn()
}));

describe("Address utils", () => {
  describe("getSuggestionFilters", () => {
    it("should return an object with countries array containing only the selected country for non-Ireland countries", () => {
      // Test with Spain
      const resultESP = getSuggestionFilters("ESP");
      expect(resultESP).toEqual({ countries: ["ESP"] });
      
      // Test with Germany
      const resultDEU = getSuggestionFilters("DEU");
      expect(resultDEU).toEqual({ countries: ["DEU"] });
    });

    it("should return an object with countries array containing IRL and GBR for Ireland", () => {
      const result = getSuggestionFilters("IRL");
      expect(result).toEqual({ countries: ["IRL", "GBR"] });
    });

    it("should handle null or undefined input by returning an empty array", () => {
      const resultNull = getSuggestionFilters(null);
      expect(resultNull).toEqual({ countries: [null] });
      
      const resultUndefined = getSuggestionFilters(undefined);
      expect(resultUndefined).toEqual({ countries: [undefined] });
    });
  });

  describe("isIreland", () => {
    it("should return true for IE and IRL country codes", () => {
      expect(isIreland("IE")).toBe(true);
      expect(isIreland("IRL")).toBe(true);
    });

    it("should return false for non-Ireland country codes", () => {
      expect(isIreland("GB")).toBe(false);
      expect(isIreland("ESP")).toBe(false);
      expect(isIreland(null)).toBe(false);
      expect(isIreland(undefined)).toBe(false);
    });
  });

  describe("shouldValidateIEorUK", () => {
    it("should return true for Ireland country codes", () => {
      expect(shouldValidateIEorUK("IE")).toBe(true);
      expect(shouldValidateIEorUK("IRL")).toBe(true);
    });

    it("should return false for non-Ireland country codes", () => {
      expect(shouldValidateIEorUK("GB")).toBe(false);
      expect(shouldValidateIEorUK("ESP")).toBe(false);
      expect(shouldValidateIEorUK(null)).toBe(false);
      expect(shouldValidateIEorUK(undefined)).toBe(false);
    });
  });

  describe("validateIEorUKPostalCode", () => {
    beforeEach(() => {
      postcodeValidator.mockReset();
    });

    it("should call postcodeValidator with IE and GB country codes", () => {
      postcodeValidator.mockReturnValueOnce(false).mockReturnValueOnce(true);
      
      const result = validateIEorUKPostalCode("D01F9C9");
      
      expect(postcodeValidator).toHaveBeenCalledWith("D01F9C9", "IE");
      expect(postcodeValidator).toHaveBeenCalledWith("D01F9C9", "GB");
      expect(result).toBe(true);
    });

    it("should return true if either IE or GB validation passes", () => {
      // IE validation passes
      postcodeValidator.mockReturnValueOnce(true).mockReturnValueOnce(false);
      expect(validateIEorUKPostalCode("D01F9C9")).toBe(true);
      
      // GB validation passes
      postcodeValidator.mockReset();
      postcodeValidator.mockReturnValueOnce(false).mockReturnValueOnce(true);
      expect(validateIEorUKPostalCode("SW1A1AA")).toBe(true);
    });

    it("should return false if both IE and GB validation fail", () => {
      postcodeValidator.mockReturnValue(false);
      expect(validateIEorUKPostalCode("12345")).toBe(false);
    });
  });

  describe("validatePostalCodeForCountry", () => {
    beforeEach(() => {
      postcodeValidator.mockReset();
      postcodeValidatorExistsForCountry.mockReset();
    });

    it("should return true if validator doesn't exist for country", () => {
      postcodeValidatorExistsForCountry.mockReturnValue(false);
      
      const result = validatePostalCodeForCountry("12345", "XX");
      
      expect(postcodeValidatorExistsForCountry).toHaveBeenCalledWith("XX");
      expect(postcodeValidator).not.toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it("should call postcodeValidator if validator exists for country", () => {
      postcodeValidatorExistsForCountry.mockReturnValue(true);
      postcodeValidator.mockReturnValue(true);
      
      const result = validatePostalCodeForCountry("28001", "ES");
      
      expect(postcodeValidatorExistsForCountry).toHaveBeenCalledWith("ES");
      expect(postcodeValidator).toHaveBeenCalledWith("28001", "ES");
      expect(result).toBe(true);
    });

    it("should return the result of postcodeValidator", () => {
      postcodeValidatorExistsForCountry.mockReturnValue(true);
      
      // Valid postal code
      postcodeValidator.mockReturnValueOnce(true);
      expect(validatePostalCodeForCountry("28001", "ES")).toBe(true);
      
      // Invalid postal code
      postcodeValidator.mockReturnValueOnce(false);
      expect(validatePostalCodeForCountry("invalid", "ES")).toBe(false);
    });
  });
});
