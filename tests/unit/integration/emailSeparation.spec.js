import Vuex from "vuex";
import { createLocalVue } from "@vue/test-utils";
import guestModule from "@/store/guest.js";
import Guest from "@/entities/Guest.js";

const localVue = createLocalVue();
localVue.use(Vuex);

describe("Email Separation Integration Tests", () => {
  let store;

  beforeEach(() => {
    store = new Vuex.Store({
      modules: {
        guest: guestModule
      }
    });

    // Initialize with a guest
    const initialGuest = new Guest({
      name: "<PERSON>",
      surname: "<PERSON><PERSON>",
      email: "<EMAIL>",
      selected: true,
      pax_type: "AD"
    });

    store.commit('guest/setGuestToList', [initialGuest], false);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("Complete flow: validate-data email should go to PMS, send-documents email should not", async () => {
    // 1. Initial state - guest has email from validate-data
    const initialGuest = store.getters['guest/getSelectedGuest'];
    expect(initialGuest.email).toBe("<EMAIL>");
    expect(initialGuest.documentsEmail).toBeNull();

    // 2. User enters different email in send-documents
    await store.dispatch('guest/UPDATE_DOCUMENTS_EMAIL', '<EMAIL>');

    // 3. Verify emails are separate
    const updatedGuest = store.getters['guest/getSelectedGuest'];
    expect(updatedGuest.email).toBe("<EMAIL>"); // Main email unchanged
    expect(updatedGuest.documentsEmail).toBe("<EMAIL>"); // Documents email set

    // 4. Simulate getFormattedGuest (what goes to PMS)
    const formattedForPMS = {
      email: updatedGuest.email || null, // This is what Confirmation.vue does
      name: updatedGuest.name,
      surname: updatedGuest.surname
    };

    // 5. Simulate email for document sending
    const emailForDocuments = updatedGuest.documentsEmail || updatedGuest.email;

    // 6. Verify correct emails are used
    expect(formattedForPMS.email).toBe("<EMAIL>"); // PMS gets validate-data email
    expect(emailForDocuments).toBe("<EMAIL>"); // Documents get send-documents email
    expect(formattedForPMS.email).not.toBe(emailForDocuments); // They should be different
  });

  it("Should handle case when user doesn't enter email in send-documents", async () => {
    // 1. Initial state - guest has email from validate-data
    const initialGuest = store.getters['guest/getSelectedGuest'];
    expect(initialGuest.email).toBe("<EMAIL>");
    expect(initialGuest.documentsEmail).toBeNull();

    // 2. User skips send-documents or doesn't enter email
    // documentsEmail remains null

    // 3. Simulate email for document sending (should fallback to main email)
    const emailForDocuments = initialGuest.documentsEmail || initialGuest.email;

    // 4. Verify fallback works
    expect(emailForDocuments).toBe("<EMAIL>");
  });

  it("Should handle multiple email updates correctly", async () => {
    // 1. Initial email update
    await store.dispatch('guest/UPDATE_DOCUMENTS_EMAIL', '<EMAIL>');
    let guest = store.getters['guest/getSelectedGuest'];
    expect(guest.documentsEmail).toBe("<EMAIL>");
    expect(guest.email).toBe("<EMAIL>");

    // 2. Second email update
    await store.dispatch('guest/UPDATE_DOCUMENTS_EMAIL', '<EMAIL>');
    guest = store.getters['guest/getSelectedGuest'];
    expect(guest.documentsEmail).toBe("<EMAIL>");
    expect(guest.email).toBe("<EMAIL>"); // Main email still unchanged

    // 3. Clear documents email
    await store.dispatch('guest/UPDATE_DOCUMENTS_EMAIL', null);
    guest = store.getters['guest/getSelectedGuest'];
    expect(guest.documentsEmail).toBeNull();
    expect(guest.email).toBe("<EMAIL>"); // Main email still unchanged
  });

  it("Should only update selected guest's documentsEmail", async () => {
    // Add a second guest (not selected)
    const secondGuest = new Guest({
      name: "Jane",
      surname: "Smith",
      email: "<EMAIL>",
      selected: false,
      pax_type: "AD"
    });

    store.state.guest.list.push(secondGuest);

    // Update documents email
    await store.dispatch('guest/UPDATE_DOCUMENTS_EMAIL', '<EMAIL>');

    // Only selected guest should be updated
    const selectedGuest = store.state.guest.list.find(g => g.selected);
    const nonSelectedGuest = store.state.guest.list.find(g => !g.selected);

    expect(selectedGuest.documentsEmail).toBe("<EMAIL>");
    expect(nonSelectedGuest.documentsEmail).toBeNull();
  });

  it("Should preserve all other guest properties when updating documentsEmail", async () => {
    const originalGuest = store.getters['guest/getSelectedGuest'];
    const originalProperties = {
      name: originalGuest.name,
      surname: originalGuest.surname,
      email: originalGuest.email,
      pax_type: originalGuest.pax_type,
      selected: originalGuest.selected
    };

    // Update documents email
    await store.dispatch('guest/UPDATE_DOCUMENTS_EMAIL', '<EMAIL>');

    const updatedGuest = store.getters['guest/getSelectedGuest'];

    // All original properties should be preserved
    expect(updatedGuest.name).toBe(originalProperties.name);
    expect(updatedGuest.surname).toBe(originalProperties.surname);
    expect(updatedGuest.email).toBe(originalProperties.email);
    expect(updatedGuest.pax_type).toBe(originalProperties.pax_type);
    expect(updatedGuest.selected).toBe(originalProperties.selected);

    // Only documentsEmail should be new
    expect(updatedGuest.documentsEmail).toBe("<EMAIL>");
  });
});
