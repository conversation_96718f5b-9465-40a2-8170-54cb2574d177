import router from "@/router/index.js";

describe("Router", () => {
  describe("afterEach hook", () => {
    it("scrolls to top after route change", () => {
      // Mock window.scrollTo
      const originalScrollTo = window.scrollTo;
      const scrollToMock = jest.fn();
      window.scrollTo = scrollToMock;
      
      try {
        // Simulate afterEach hook by triggering it manually
        router.afterHooks.forEach(hook => {
          hook({ name: "SomePage" }, { name: "PreviousPage" });
        });
        
        // Verify that window.scrollTo was called with the correct arguments
        expect(scrollToMock).toHaveBeenCalledWith(0, 0);
      } finally {
        // Restore original scrollTo to avoid affecting other tests
        window.scrollTo = originalScrollTo;
      }
    });
  });
});