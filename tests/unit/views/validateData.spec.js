import Vuex from "vuex";
import Vuelidate from "vuelidate";
import { mount, createLocalVue } from "@vue/test-utils";
import ValidateData from "@/views/ValidateData";
import ValidateDataCard from "@/components/shared/validateDataCard.vue";
import brandModule from "@/store/brand";
import {
	defaultConfig as brandAciConfig,
	defaultConfig,
	fillFromHolderConfig,
	noResidenceCountryConfig,
	reservationHolderConfig,
} from "../../../mocks/modules/brand/data";
import {
	receptionReservation,
	selectedReservation,
} from "../../../mocks/modules/integration/data";
import {
	formattedSuggestions,
	defaultNormalizedSelection,
	singleItemOption
} from "../../../mocks/modules/aws/data";
import loading from "@/store/loading";
import reservationsModule from "@/store/reservations";
import guestModule from "@/store/guest";
import { guestData } from "../../../mocks/data/guestData";
import modal from "@/store/modal";
import queryParamsModule from "@/store/queryParams";
import scanModule from "@/store/scan";
import appModule from "@/store/app";
import { i18n } from "@/locales";
const localVue = createLocalVue();
import { Geo } from "@aws-amplify/geo";
// need to import vuex
localVue.use(Vuex);
// if the components mounted reference $v we need to import Vuelidate
localVue.use(Vuelidate);
import { ccaaList, provinces } from "@/utils/ccaa";
import { countries } from "@/utils/countries";
import repository from "@/repository/repositoryFactory.js";

const apiEmail = repository.get("email");
// This tests big picture functionality
// Ex: page methods, page modals...
describe("Validate Data logic", () => {
	let store;
	let wrapper;
	let mockRoute;
	let mockRouter;
	let brand;
	let guest;
	let reservations;
	let scan;
	let queryParams;

	beforeEach(async () => {
		mockRouter = { push: jest.fn() };
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getSelectedGuest: () => {
					return {
						address: {
							house_number: null,
							postcode: null,
							province: null,
							street: null,
						},
						birthday_date: "1974-04-07",
						date_of_expiry: "2050-06-16",
						date_of_issue: "2017-08-16",
						document_number: "99999999R",
						document_subtype: "X",
						document_type: "passport",
						gender: "female",
						height: "1.74m",
						residence_country: "PRT",
						issuing_institution: "SEF-SERV ESTR E FRONTEIRAS",
						name: "Ines",
						nationality: "PRT",
						place_of_birth: "Mação Santarém",
						second_surname: "De Magalhães",
						signature: null,
						surname: "Garção",
					};
				},
			},
		};
		queryParams = queryParamsModule;
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: brandAciConfig.data,
			},
		};
		scan = scanModule;
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
					queryParams,
					scan,
				},
			});
		};
	});

	afterEach(() => {
		jest.resetAllMocks();
	});

	it("Modal appears if form data is too different than scanned data", async () => {
		wrapper = await mount(ValidateData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		// Mock methods that involve Geo
		wrapper.vm.handleChangeAddress = jest.fn(() => null);
		wrapper.vm.manageAddressInfo = jest.fn(() => null);
		wrapper.vm.autofillFields = jest.fn(() => null);

		await localVue.nextTick();

		const continueBtn = wrapper.find('[data-test="validate-data-button"]');
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		await wrapper.find('[data-test="name"]').setValue("Name");

		await wrapper.find('[data-test="surname"]').setValue("Surname");
		await wrapper
			.find('[data-test="second_surname"]')
			.setValue("SecondSurname");
		await wrapper.find("#birthday_date").setValue("07-04-1975");
		await wrapper.find('[data-test="gender"]').setValue("male");
		await wrapper.find('[data-test="document_type"]').setValue("identity_card");
		await wrapper.find('[data-test="document_number"]').setValue("1700043");
		await wrapper.find('[data-test="address"]').setValue("fake address");
		expect(
			wrapper.find('[data-test="residence_country"]').element.value,
		).toEqual("Portugal");
		await wrapper.find('[data-test="postal_code"]').setValue("1000");

		await continueBtn.trigger("click");
		await localVue.nextTick();
		await localVue.nextTick();
		await localVue.nextTick();
		expect(wrapper.vm.validationErrors).toEqual([]);
		expect(storeDispatch).toBeCalledWith("modal/SET_TYPE", "info");
		expect(storeDispatch).toBeCalledWith("modal/SET_NAME", "differences");
		expect(storeDispatch).toBeCalledWith("modal/SET_TITLE", "");
		expect(wrapper.vm.$router.push).not.toBeCalled();

		wrapper.destroy();
	});

	it("User clicks on submit, but all data is not filled, validationErrors should contain data", async () => {
		wrapper = await mount(ValidateData, {
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		wrapper.vm.handleChangeAddress = jest.fn(() => null);
		wrapper.vm.manageAddressInfo = jest.fn(() => null);
		wrapper.vm.autofillFields = jest.fn(() => null);

		await localVue.nextTick();

		const continueBtn = wrapper.find('[data-test="validate-data-button"]');

		await wrapper.find('[data-test="name"]').setValue("");

		await continueBtn.trigger("click");
		await localVue.nextTick();
		expect(wrapper.vm.validationErrors).toEqual([
			{
				active: "true",
				fill_from_holder: "false",
				hasError: true,
				maxLength: "50",
				minLength: "2",
				name: "name",
				position: 1,
				type: "text",
				value: "",
				autocompleteData: {
					name: "firstName",
					autocomplete: "given-name",
				},
			},
			{
				active: "true",
				fill_from_holder: "false",
				hasError: true,
				maxLength: "20",
				minLength: "3",
				name: "postal_code",
				position: 14,
				type: "text",
				value: "",
				autocompleteData: {
					name: "postal_code",
					autocomplete: "off",
				},
			},
		]);
		await wrapper.find('[data-test="name"]').setValue("Ines");
		await wrapper.find('[data-test="surname"]').setValue("");

		await continueBtn.trigger("click");
		await localVue.nextTick();
		expect(wrapper.vm.validationErrors).toEqual([
			{
				active: "true",
				fill_from_holder: "false",
				hasError: true,
				maxLength: "50",
				minLength: "2",
				name: "surname",
				position: 2,
				type: "text",
				value: "",
				autocompleteData: {
					name: "lastName",
					autocomplete: "family-name",
				},
			},
			{
				active: "true",
				fill_from_holder: "false",
				hasError: true,
				maxLength: "20",
				minLength: "3",
				name: "postal_code",
				position: 14,
				type: "text",
				value: "",
				autocompleteData: {
					name: "postal_code",
					autocomplete: "off",
				},
			},
		]);
		wrapper.destroy();
	});

	it("Modal doesn't appear if form data is equal to the scanned data", async () => {
		mockRoute = { name: "SelectRoom" };
		wrapper = await mount(ValidateData, {
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		// Mock methods that involve Geo
		wrapper.vm.handleChangeAddress = jest.fn(() => null);
		wrapper.vm.manageAddressInfo = jest.fn(() => null);
		wrapper.vm.autofillFields = jest.fn(() => null);

		await localVue.nextTick();

		const continueBtn = wrapper.find('[data-test="validate-data-button"]');
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");

		await wrapper.find('[data-test="name"]').setValue("Ines");

		await wrapper.find('[data-test="surname"]').setValue("Garção");
		await wrapper.find('[data-test="second_surname"]').setValue("De Magalhães");
		await wrapper.find("#birthday_date").setValue("07-04-1974");
		await wrapper.find('[data-test="gender"]').setValue("female");
		await wrapper.find('[data-test="document_number"]').setValue("1700044");
		await wrapper.find('[data-test="address"]').setValue("fake address");
		expect(
			wrapper.find('[data-test="residence_country"]').element.value,
		).toEqual("Portugal");
		await wrapper.find('[data-test="postal_code"]').setValue("1000");

		await continueBtn.trigger("click");
		await localVue.nextTick();
		expect(wrapper.vm.validationErrors).toEqual([]);
		expect(storeDispatch).not.toBeCalledWith("modal/SET_NAME", "differences");
		expect(wrapper.vm.$router.push).toBeCalledWith({ name: "ChildsData" });

		wrapper.destroy();
	});
	it("If email is filled and email service returns that email is not valid, email modal error should pop", async () => {
		apiEmail.validateEmail = jest.fn(async () =>
			Promise.resolve({
				valid: false,
			}),
		);
		wrapper = await mount(ValidateData, {
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		wrapper.vm.handleChangeAddress = jest.fn(() => null);
		wrapper.vm.manageAddressInfo = jest.fn(() => null);
		wrapper.vm.autofillFields = jest.fn(() => null);

		await localVue.nextTick();

		const continueBtn = wrapper.find('[data-test="validate-data-button"]');
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");

		await wrapper.find('[data-test="postal_code"]').setValue("8888");

		await continueBtn.trigger("click");
		await localVue.nextTick();

		await wrapper.find('[data-test="email"]').setValue("<EMAIL>");

		await continueBtn.trigger("click");
		await localVue.nextTick();
		await localVue.nextTick();
		await localVue.nextTick();
		expect(await apiEmail.validateEmail).toBeCalledWith("<EMAIL>", 1);
		expect(await storeDispatch).toBeCalledWith(
			"modal/SET_NAME",
			"emailValidation",
		);

		wrapper.destroy();
	});

	it("If email is filled and email service returns that email is valid, email modal should not pop up", async () => {
		apiEmail.validateEmail = jest.fn(async () =>
			Promise.resolve({
				valid: true,
			}),
		);
		mockRoute = { name: "SelectRoom" };
		wrapper = mount(ValidateData, {
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		wrapper.vm.handleChangeAddress = jest.fn(() => null);
		wrapper.vm.manageAddressInfo = jest.fn(() => null);
		wrapper.vm.autofillFields = jest.fn(() => null);

		await localVue.nextTick();

		const continueBtn = wrapper.find('[data-test="validate-data-button"]');
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");

		await wrapper.find('[data-test="postal_code"]').setValue("8888");

		await continueBtn.trigger("click");
		await localVue.nextTick();

		await wrapper.find('[data-test="email"]').setValue("<EMAIL>");

		await continueBtn.trigger("click");
		await localVue.nextTick();
		expect(await apiEmail.validateEmail).toBeCalledWith("<EMAIL>", 1);
		expect(await storeDispatch).not.toBeCalledWith(
			"modal/SET_NAME",
			"emailValidation",
		);

		wrapper.destroy();
	});

	it("If email validation throws an error, modal should pop up", async () => {
		apiEmail.validateEmail = jest.fn(async () => {
			throw new Error("IM AN ERROR!");
		});
		wrapper = mount(ValidateData, {
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		wrapper.vm.handleChangeAddress = jest.fn(() => null);
		wrapper.vm.manageAddressInfo = jest.fn(() => null);
		wrapper.vm.autofillFields = jest.fn(() => null);

		await localVue.nextTick();

		const continueBtn = wrapper.find('[data-test="validate-data-button"]');
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");

		await wrapper.find('[data-test="postal_code"]').setValue("8888");

		await continueBtn.trigger("click");
		await localVue.nextTick();

		await wrapper.find('[data-test="email"]').setValue("<EMAIL>");

		await continueBtn.trigger("click");
		await localVue.nextTick();
		await localVue.nextTick();
		expect(await apiEmail.validateEmail).toBeCalledWith("<EMAIL>", 1);
		expect(await storeDispatch).toBeCalledWith(
			"modal/SET_NAME",
			"emailValidation",
		);

		wrapper.destroy();
	});

	it("If residence_country is selected, SearchForSuggestions is called with filters", async () => {
		// Mock Geo methods
		Geo.searchForSuggestions = jest.fn(async () =>
			Promise.resolve(formattedSuggestions),
		);
		Geo.searchByText = jest.fn(async () =>
			Promise.resolve(defaultNormalizedSelection),
		);

		wrapper = mount(ValidateData, {
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});
		await localVue.nextTick();

		wrapper.vm.manageAddressInfo({
			name: "address",
			hasError: "false",
			active: "true",
			required: "true",
			value: "test street",
		});

		expect(Geo.searchForSuggestions).toBeCalledWith("test street", {
			countries: ["PRT"], // Already set in store
		});
	});

	it("If residence_country is not selected, SearchForSuggestions is called without filters", async () => {
		// Mock Geo methods
		Geo.searchForSuggestions = jest.fn(async () =>
			Promise.resolve(formattedSuggestions),
		);
		Geo.searchByText = jest.fn(async () =>
			Promise.resolve(defaultNormalizedSelection),
		);

		wrapper = mount(ValidateData, {
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});
		await localVue.nextTick();
		wrapper.vm.selectedCountry = null;
		wrapper.vm.manageAddressInfo({
			name: "address",
			hasError: "false",
			active: "true",
			required: "true",
			value: "test street",
		});

		expect(Geo.searchForSuggestions).toBeCalledWith("test street", {});
	});

it("If residence_country is Ireland, SearchForSuggestions is called with IRL and GBR filters", async () => {
  // Mock Geo methods
  Geo.searchForSuggestions = jest.fn(async () =>
    Promise.resolve(formattedSuggestions),
  );
  Geo.searchByText = jest.fn(async () =>
    Promise.resolve(defaultNormalizedSelection),
  );

  wrapper = mount(ValidateData, {
    mocks: {
      $route: mockRoute,
      $router: mockRouter,
    },
    store,
    localVue,
    i18n,
    attachTo: document.body,
  });
  await localVue.nextTick();
  
  // Set selectedCountry to Ireland
  wrapper.vm.selectedCountry = "IRL";
  
  wrapper.vm.manageAddressInfo({
    name: "address",
    hasError: "false",
    active: "true",
    required: "true",
    value: "test street dublin",
  });

  expect(Geo.searchForSuggestions).toBeCalledWith("test street dublin", {
    countries: ["IRL", "GBR"],
  });
});

it("If residence_country is Spain, SearchForSuggestions is called with only ESP filter", async () => {
  // Mock Geo methods
  Geo.searchForSuggestions = jest.fn(async () =>
    Promise.resolve(formattedSuggestions),
  );
  Geo.searchByText = jest.fn(async () =>
    Promise.resolve(defaultNormalizedSelection),
  );

  wrapper = mount(ValidateData, {
    mocks: {
      $route: mockRoute,
      $router: mockRouter,
    },
    store,
    localVue,
    i18n,
    attachTo: document.body,
  });
  await localVue.nextTick();
  
  // Set selectedCountry to Spain
  wrapper.vm.selectedCountry = "ESP";
  
  wrapper.vm.manageAddressInfo({
    name: "address",
    hasError: "false",
    active: "true",
    required: "true",
    value: "calle test madrid",
  });

  expect(Geo.searchForSuggestions).toBeCalledWith("calle test madrid", {
    countries: ["ESP"],
  });
});
});

describe("Validate Data with fill from holder logic and no guest info", () => {
	let store;
	let wrapper;
	let mockRouter;
	let brand;
	let guest;
	let reservations;
	let scan;
	let queryParams;

	beforeEach(async () => {
		mockRouter = { push: jest.fn() };
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getSelectedGuest: () => {
					return {
						name: "",
						surname: "Surname",
						second_surname: "Second Surname",
						full_name: null,
						gender: "female",
						birthday_date: "2000-01-01",
						email: null,
						telephone: null,
						holder: false,
						document_type: "passport",
						document_number: "00000000X",
						date_of_issue: "2001-01-01",
						date_of_expiry: "2060-01-01",
						address: {
							street: null,
							postal_code: null,
							province: null,
							city: null,
							CCAA: null,
							country: null,
						},
						nationality: "DEU",
						residence_country: "DEU",
						validated: false,
					};
				},
				getHolderGuest: () => {
					return {
						name: "",
						surname: "Viyuela",
						second_surname: "Second",
						full_name: "Pepe Viyuela Second",
						gender: "male",
						birthday_date: "1990-01-01",
						email: "<EMAIL>",
						telephone: "+34666666666",
						holder: true,
						document_type: "identity_card",
						document_number: "99999999R",
						date_of_issue: "",
						date_of_expiry: "2050-01-01",
						address: {
							street: "Carrer Cala Mitjana Palma",
							postal_code: "07009",
							province: null,
							city: "Palma",
							CCAA: null,
							country: null,
						},
						nationality: "ESP",
						residence_country: "Spain",
						validated: true,
					};
				},
			},
		};
		queryParams = queryParamsModule;
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: fillFromHolderConfig.data,
			},
		};
		scan = scanModule;
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
					queryParams,
					scan,
				},
			});
		};
	});

	afterEach(() => {
		jest.resetAllMocks();
	});

	it("Replaces only missing values from holder", async () => {
		wrapper = await mount(ValidateData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		// Mock methods that involve Geo
		wrapper.vm.handleChangeAddress = jest.fn(() => null);
		wrapper.vm.manageAddressInfo = jest.fn(() => null);

		await localVue.nextTick();

		expect(wrapper.find('[data-test="name"]').element.value).toEqual("");
		expect(wrapper.find('[data-test="surname"]').element.value).toEqual(
			"Surname",
		);
		expect(wrapper.find('[data-test="second_surname"]').element.value).toEqual(
			"Second Surname",
		);
		expect(wrapper.find("#birthday_date").element.value).toEqual("01-01-2000");
		expect(wrapper.find('[data-test="gender"]').element.value).toEqual(
			"female",
		);
		expect(wrapper.find('[data-test="document_type"]').element.value).toEqual(
			"passport",
		);
		expect(wrapper.find('[data-test="document_number"]').element.value).toEqual(
			"00000000X",
		);
		expect(wrapper.find("#date_of_issue").element.value).toEqual("01-01-2001");
		expect(wrapper.find("#date_of_expiry").element.value).toEqual("01-01-2060");
		expect(wrapper.find('[data-test="nationality"]').element.value).toEqual(
			"Germany",
		);
		expect(
			wrapper.find('[data-test="residence_country"]').element.value,
		).toEqual("Germany");
		expect(wrapper.find('[data-test="address"]').element.value).toEqual(
			"Carrer Cala Mitjana Palma",
		);
		expect(wrapper.find('[data-test="postal_code"]').element.value).toEqual(
			"07009",
		);
		await localVue.nextTick();
		expect(wrapper.find('[data-test="CCAA"]').exists()).toBe(false);
		expect(wrapper.find('[data-test="province"]').exists()).toBe(false);
		expect(wrapper.find(".vti__input").element.value).toEqual("+34666666666");
		expect(wrapper.find('[data-test="email"]').element.value).toEqual(
			"<EMAIL>",
		);
		expect(wrapper.find('[data-test="municipality"]').element.value).toEqual(
			"Palma",
		);
		expect(wrapper.find('[data-test="region"]').element.value).toEqual("");
		expect(wrapper.find('[data-test="subregion"]').element.value).toEqual("");

		wrapper.destroy();
	});
});

describe("Validate Data with fill form holder logic and guest info", () => {
	let store;
	let wrapper;
	let mockRouter;
	let brand;
	let guest;
	let reservations;
	let scan;
	let queryParams;

	beforeEach(async () => {
		mockRouter = { push: jest.fn() };
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getSelectedGuest: () => {
					return {
						name: null,
						surname: null,
						second_surname: null,
						full_name: null,
						gender: null,
						birthday_date: null,
						email: null,
						telephone: null,
						holder: false,
						document_type: null,
						document_number: null,
						date_of_issue: null,
						date_of_expiry: null,
						address: {
							street: null,
							postal_code: null,
							province: null,
							city: null,
							CCAA: null,
							country: null,
						},
						nationality: null,
						residence_country: null,
						validated: false,
					};
				},
				getHolderGuest: () => {
					return {
						name: "Pepe",
						surname: "Viyuela",
						second_surname: "Second",
						full_name: "Pepe Viyuela Second",
						gender: "male",
						birthday_date: "1990-01-01",
						email: "<EMAIL>",
						telephone: "+34666666666",
						holder: true,
						document_type: "identity_card",
						document_number: "99999999R",
						date_of_issue: "1990-01-01",
						date_of_expiry: "2050-01-01",
						address: {
							street: "Carrer Cala Mitjana Palma",
							postal_code: "07009",
							province: null,
							city: "Palma",
							CCAA: null,
							country: null,
						},
						nationality: "ESP",
						residence_country: "Spain",
						validated: true,
					};
				},
			},
		};
		queryParams = queryParamsModule;
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: fillFromHolderConfig.data,
			},
		};
		scan = scanModule;
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
					queryParams,
					scan,
				},
			});
		};
	});

	afterEach(() => {
		jest.resetAllMocks();
	});

	it("Replaces all the form info if no data set like optional scan", async () => {
		wrapper = await mount(ValidateData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		// Mock methods that involve Geo
		wrapper.vm.handleChangeAddress = jest.fn(() => null);
		wrapper.vm.manageAddressInfo = jest.fn(() => null);
		wrapper.vm.autofillFields = jest.fn(() => null);

		await localVue.nextTick();

		expect(wrapper.find('[data-test="name"]').element.value).toEqual("Pepe");
		expect(wrapper.find('[data-test="surname"]').element.value).toEqual(
			"Viyuela",
		);
		expect(wrapper.find('[data-test="second_surname"]').element.value).toEqual(
			"Second",
		);
		expect(wrapper.find("#birthday_date").element.value).toEqual("01-01-1990");
		expect(wrapper.find('[data-test="gender"]').element.value).toEqual("male");
		expect(wrapper.find('[data-test="document_type"]').element.value).toEqual(
			"identity_card",
		);
		expect(wrapper.find('[data-test="document_number"]').element.value).toEqual(
			"99999999R",
		);
		expect(wrapper.find("#date_of_issue").element.value).toEqual("01-01-1990");
		expect(wrapper.find("#date_of_expiry").element.value).toEqual("01-01-2050");
		expect(wrapper.find('[data-test="nationality"]').element.value).toEqual(
			"Spain",
		);
		expect(
			wrapper.find('[data-test="residence_country"]').element.value,
		).toEqual("Spain");
		expect(wrapper.find('[data-test="address"]').element.value).toEqual(
			"Carrer Cala Mitjana Palma",
		);
		expect(wrapper.find('[data-test="postal_code"]').element.value).toEqual(
			"07009",
		);
		await localVue.nextTick();
		expect(wrapper.find('[data-test="CCAA"]').element.value).toEqual("04");
		await localVue.nextTick();
		expect(wrapper.find('[data-test="province"]').element.value).toEqual("07");
		expect(wrapper.find(".vti__input").element.value).toEqual("666 66 66 66");
		expect(wrapper.find('[data-test="email"]').element.value).toEqual(
			"<EMAIL>",
		);
		expect(wrapper.find('[data-test="municipality"]').element.value).toEqual(
			"Palma",
		);
		expect(wrapper.find('[data-test="region"]').exists()).toBe(false);
		expect(wrapper.find('[data-test="subregion"]').exists()).toBe(false);

		wrapper.destroy();
	});
});

describe("Test validate data input depending of residence country", () => {
	let store;
	let wrapper;
	let mockRouter;
	let brand;
	let guest;
	let reservations;
	let scan;
	let queryParams;

	beforeEach(async () => {
		mockRouter = { push: jest.fn() };
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getSelectedGuest: () => {
					return {
						address: {
							house_number: null,
							postcode: null,
							province: null,
							street: null,
						},
						birthday_date: "1974-04-07",
						date_of_expiry: "2050-06-16",
						date_of_issue: "2017-08-16",
						document_number: "99999999R",
						document_subtype: "X",
						document_type: "passport",
						gender: "female",
						height: "1.74m",
						residence_country: "PRT",
						issuing_institution: "SEF-SERV ESTR E FRONTEIRAS",
						name: "Ines",
						nationality: "PRT",
						place_of_birth: "Mação Santarém",
						second_surname: "De Magalhães",
						signature: null,
						surname: "Garção",
					};
				},
			},
		};
		queryParams = queryParamsModule;
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: {
					active: true,
					identification: {
						child_form: [
							[
								{
									active: "false",
									required: "false",
									name: "name",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
								{
									active: "false",
									required: "false",
									name: "surname",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
								{
									active: "true",
									required: "true",
									name: "birthday",
									type: "date",
								},
							],
						],
						reservation_inputs: [
							[
								{
									name: "reservation_code",
									type: "text",
									minLength: "3",
									maxLength: "20",
									active: "true",
								},
								{
									name: "last_name",
									type: "text",
									minLength: "2",
									maxLength: "50",
									active: "true",
								},
							],
						],
						reservation_filters: [
							[
								{
									name: "check_in",
									type: "date",
								},
							],
							[
								{
									name: "check_out",
									type: "date",
								},
							],
						],
						validate_data_scan: [
							[
								{
									position: 1,
									active: "true",
									fill_from_holder: "false",
									name: "name",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
								{
									position: 2,
									active: "true",
									fill_from_holder: "false",
									name: "surname",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
								{
									position: 3,
									active: "true",
									fill_from_holder: "false",
									name: "second_surname",
									required: "false",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
								{
									position: 4,
									active: "true",
									fill_from_holder: "false",
									name: "birthday_date",
									type: "date",
								},
								{
									position: 5,
									active: "true",
									fill_from_holder: "false",
									name: "gender",
									type: "select",
									options: ["male", "female"],
								},
								{
									position: 6,
									active: "true",
									fill_from_holder: "false",
									name: "document_type",
									type: "select",
									options: ["identity_card", "passport"],
								},
								{
									position: 7,
									active: "true",
									fill_from_holder: "false",
									name: "document_number",
									type: "text",
									minLength: "4",
									maxLength: "20",
								},
								{
									position: 8,
									active: "true",
									fill_from_holder: "false",
									name: "date_of_issue",
									type: "date",
								},
								{
									position: 9,
									active: "true",
									fill_from_holder: "false",
									name: "date_of_expiry",
									type: "date",
								},
								{
									position: 10,
									active: "true",
									fill_from_holder: "false",
									name: "nationality",
									options: [...countries],
									type: "autocomplete",
									countryInput: true,
								},
								{
									position: 11,
									active: "true",
									fill_from_holder: "false",
									name: "residence_country",
									options: [...countries],
									type: "autocomplete",
									countryInput: true,
								},
								{
									position: 12,
									active: "true",
									fill_from_holder: "false",
									name: "address",
									type: "autocomplete",
									minLength: "5",
									maxLength: "500",
								},
								{
									position: 14,
									active: "true",
									fill_from_holder: "false",
									name: "postal_code",
									type: "text",
									minLength: "3",
									maxLength: "20",
								},
								{
									position: 15,
									active: "true",
									fill_from_holder: "false",
									required: "true",
									name: "CCAA",
									type: "select",
									options: [...ccaaList],
								},
								{
									position: 16,
									active: "true",
									fill_from_holder: "false",
									required: "false",
									name: "province",
									type: "select",
									options: [...provinces],
								},
								{
									position: 17,
									active: "false",
									fill_from_holder: "false",
									required: "false",
									name: "telephone",
									type: "phone",
									minLength: "2",
									maxLength: "50",
								},
								{
									position: 18,
									active: "true",
									fill_from_holder: "false",
									required: "false",
									name: "email",
									type: "email",
								},
								{
									position: 13,
									active: "true",
									fill_from_holder: "false",
									required: "false",
									name: "municipality",
									type: "text",
									minLength: "2",
									maxLength: "100",
								},
								{
									position: 17,
									active: "true",
									fill_from_holder: "false",
									required: "false",
									name: "region",
									type: "text",
									minLength: "2",
									maxLength: "100",
								},
								{
									position: 18,
									active: "true",
									fill_from_holder: "false",
									required: "false",
									name: "subregion",
									type: "text",
									minLength: "2",
									maxLength: "100",
								},
							],
						],
					},
					max_attempts_reservation: 3,
					child_required_identity_documents_age: 18,
					optional_scan: true,
					max_attempts_child: 3,
					max_attempts_document: 3,
					partial_checkin: true,
					room_type_selection: false,
					max_attempts_telephone: 3,
					telephone: true,
					telephone_notifications: true,
					comments: true,
					signed_documents: true,
					scan_children_like_adults: false,
					custom_scan_text: false,
					send_identity_documents_to_PMS: true,
					time_limit_checkin: 0,
					custom_confirmation_text: false,
					send_signed_documents_to_reception: false,
					max_attempts_validate_telephone_code: 3,
					max_attempts_email_validation: 3,
					allow_expired_documents: false,
					reception_signature: false,
				},
			},
		};
		scan = scanModule;
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
					queryParams,
					scan,
				},
			});
		};

		wrapper = await mount(ValidateData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});
	});

	afterEach(() => {
		jest.resetAllMocks();
	});

	it("Hide CCAA input and show region and subregion if guest is not spanish", async () => {
		// Mock methods that involve Geo
		wrapper.vm.handleChangeAddress = jest.fn(() => null);
		wrapper.vm.manageAddressInfo = jest.fn(() => null);
		wrapper.vm.autofillFields = jest.fn(() => null);

		await localVue.nextTick();

		const ccaaInput = wrapper.find('[data-test="CCAA"]');
		const region = wrapper.find('[data-test="region"]');
		const subregion = wrapper.find('[data-test="subregion"]');

		expect(ccaaInput.exists()).toBe(false);
		expect(region.isVisible()).toBe(true);
		expect(subregion.isVisible()).toBe(true);

		wrapper.destroy();
	});

	it("Shows CCAA and provice inputs and hides region and subregion if guest is spanish", async () => {
		// Mock methods that involve Geo
		wrapper.vm.handleChangeAddress = jest.fn(() => null);
		wrapper.vm.manageAddressInfo = jest.fn(() => null);
		wrapper.vm.autofillFields = jest.fn(() => null);

		await localVue.nextTick();
		await wrapper.find('[data-test="residence_country"]').setValue("ESP");
		await localVue.nextTick();
		await new Promise((r) => setTimeout(r, 200));

		const ccaaInput = wrapper.find('[data-test="CCAA"]');
		const region = wrapper.find('[data-test="region"]');
		const subregion = wrapper.find('[data-test="subregion"]');

		expect(ccaaInput.isVisible()).toBe(true);
		expect(region.exists()).toBe(false);
		expect(subregion.exists()).toBe(false);

		wrapper.destroy();
	});
});

describe("Test document support number input depending on document type and nationality", () => {
	let store;
	let wrapper;
	let mockRouter;
	let brand;
	let guest;
	let reservations;
	let scan;
	let queryParams;

	beforeEach(async () => {
		const brandConfig = brandAciConfig.data;
		brandConfig.identification.validate_data_scan[0].push({
			position: 19,
			active: "true",
			fill_from_holder: "false",
			required: "false",
			name: "document_support_number",
			type: "text",
			minLength: "2",
			maxLength: "100",
		});

		mockRouter = { push: jest.fn() };
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getSelectedGuest: () => {
					return {
						address: {
							house_number: null,
							postcode: null,
							province: null,
							street: null,
						},
						birthday_date: "1974-04-07",
						date_of_expiry: "2050-06-16",
						date_of_issue: "2017-08-16",
						document_number: "99999999R",
						document_support_number: "CAA000000",
						document_subtype: "X",
						document_type: "identity_card",
						gender: "female",
						height: "1.74m",
						residence_country: "FRA",
						issuing_institution: "SEF-SERV ESTR E FRONTEIRAS",
						name: "Ines",
						nationality: "ESP",
						place_of_birth: "Mação Santarém",
						second_surname: "De Magalhães",
						signature: null,
						surname: "Garção",
					};
				},
			},
		};
		queryParams = queryParamsModule;
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: brandConfig,
			},
		};
		scan = scanModule;
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
					queryParams,
					scan,
				},
			});
		};

		wrapper = await mount(ValidateData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});
	});

	afterEach(() => {
		jest.resetAllMocks();
	});

	it("Show support number input if it's a spanish DNI", async () => {
		const supportNumber = wrapper.find('[data-test="document_support_number"]');
		expect(supportNumber.isVisible()).toBe(true);
		wrapper.destroy();
	});
	it("Shouldn't show support number input if it's a spanish passport", async () => {
		const selectedGuest = guest.getters.getSelectedGuest();
		selectedGuest.document_type = "passport";
		const updatedGuest = {
			...guest,
			getters: {
				getSelectedGuest: () => selectedGuest,
			},
		};
		wrapper = await mount(ValidateData, {
			mocks: {
				$router: mockRouter,
			},
			store: store(updatedGuest),
			localVue,
			i18n,
			attachTo: document.body,
		});
		const supportNumber = wrapper.find('[data-test="document_support_number"]');
		expect(supportNumber.exists()).toBe(false);
		wrapper.destroy();
	});
	it("Shouldn't show support number input if it's a non spanish DNI", async () => {
		const selectedGuest = guest.getters.getSelectedGuest();
		selectedGuest.nationality = "FRA";
		const updatedGuest = {
			...guest,
			getters: {
				getSelectedGuest: () => selectedGuest,
			},
		};
		wrapper = await mount(ValidateData, {
			mocks: {
				$router: mockRouter,
			},
			store: store(updatedGuest),
			localVue,
			i18n,
			attachTo: document.body,
		});
		const supportNumber = wrapper.find('[data-test="document_support_number"]');
		expect(supportNumber.exists()).toBe(false);
		wrapper.destroy();
	});
});

describe("Brand does not allow to modify reservation holder and the user is holder", () => {
	let store;
	let wrapper;
	let mockRouter;
	let brand;
	let guest;
	let reservations;
	let scan;
	let queryParams;

	beforeEach(async () => {
		mockRouter = { push: jest.fn() };
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getSelectedGuest: () => {
					return guestData[0];
				},
			},
		};
		queryParams = queryParamsModule;
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: reservationHolderConfig.data,
			},
		};
		scan = scanModule;
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
					queryParams,
					scan,
				},
			});
		};
	});

	afterEach(() => {
		jest.resetAllMocks();
	});

	it("Name and surname inputs should be disabled and already filled", async () => {
		wrapper = await mount(ValidateData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		await localVue.nextTick();

		expect(wrapper.find('[data-test="name"]').element.disabled).toBe(true);
		expect(wrapper.find('[data-test="surname"]').element.disabled).toBe(true);
		expect(wrapper.find('[data-test="name"]').element.value).toBe("Pepe");
		expect(wrapper.find('[data-test="surname"]').element.value).toBe("Viyuela");

		wrapper.destroy();
	});
});

describe("Second surname is mandatory if nationality is Spanish config", () => {
	let store;
	let wrapper;
	let mockRouter;
	let brand;
	let guest;
	let reservations;
	let scan;
	let queryParams;

	beforeEach(async () => {
		mockRouter = { push: jest.fn() };
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getSelectedGuest: () => {
					return guestData[0];
				},
			},
		};
		queryParams = queryParamsModule;
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: {
          ...defaultConfig.data,
          second_surname_required_for_spanish: true, 
        },
			},
		};
		scan = scanModule;
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
					queryParams,
					scan,
				},
			});
		};
	});

	afterEach(() => {
		jest.resetAllMocks();
	});

	it("second surname should be mandatory", async () => {
		wrapper = await mount(ValidateData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		await localVue.nextTick();

		let secondSurnameInput = wrapper.vm.inputDataList.find(
			(input) => input.name === "second_surname"
		);

		expect(secondSurnameInput.required).toBe("true");

		wrapper.vm.autocompleteSelect({ value: "FRA" }, { name: "nationality" });

		await localVue.nextTick();
	
		secondSurnameInput = wrapper.vm.inputDataList.find(
			(input) => input.name === "second_surname"
		);
		expect(secondSurnameInput.required).toBe("false");

		wrapper.destroy();
	});
});

// This will test form functionality and how inputs interact with each other
// Ex: if CCAA changes, how other inputs react to this
describe("Validate data without scanned data", () => {
	let store;
	let wrapper;
	let brand;
	let guest;
	let reservations;
	let scan;
	let queryParams;
	let mockRouter;

	beforeEach(async () => {
		mockRouter = {
			push: jest.fn(() => Promise.resolve()),
		};
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getSelectedGuest: () => {
					return {
						address: {
							house_number: null,
							postcode: null,
							province: null,
							street: null,
						},
						birthday_date: null,
						date_of_expiry: "2050-06-16",
						date_of_issue: "2017-08-16",
						document_number: "99999999R",
						document_subtype: "X",
						document_type: "passport",
						gender: "female",
						residence_country: "PRT",
						issuing_institution: "SEF-SERV ESTR E FRONTEIRAS",
						name: "Ines",
						nationality: "PRT",
						place_of_birth: "Mação Santarém",
						second_surname: "De Magalhães",
						signature: null,
						surname: "Garção",
					};
				},
			},
		};
		queryParams = {
			...queryParamsModule,
			state: {
				data: {
					manualProcess: true,
				},
			},
		};
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: brandAciConfig.data,
			},
		};
		scan = scanModule;
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
					queryParams,
					scan,
				},
			});
		};
	});

	it("Default config", async () => {
		Geo.searchForSuggestions = jest.fn(async () =>
			Promise.resolve(formattedSuggestions),
		);
		Geo.searchByText = jest.fn(async () =>
			Promise.resolve(defaultNormalizedSelection),
		);
		// In order to test inputs with debounce
		jest.mock("lodash/debounce", () => jest.fn((fn) => fn));
		wrapper = await mount(ValidateData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});
		await localVue.nextTick();
		//spys
		jest.spyOn(wrapper.vm, "inputChanged");
		jest.spyOn(wrapper.vm.$store, "dispatch");
		jest.spyOn(wrapper.vm, "autocompleteSelect");
		// variables
		const addressInput = wrapper.find('[data-test="address"]');
		const addressOptions = wrapper.find('[data-test="address-list"]');
		// const continueBtn = wrapper.find('[data-test="validate-data-button"]');

		await addressInput.setValue("miquel forteza y piña");

		expect(addressOptions.exists()).toBe(true);
		expect(addressOptions.isVisible()).toBe(false);

		await new Promise((r) => setTimeout(r, 1000));

		expect(wrapper.find('[data-test="CCAA"]').exists()).toBe(false);
		expect(wrapper.find('[data-test="province"]').exists()).toBe(false);

		// Selected option, so autocompleteOption should run
		expect(wrapper.vm.autocompleteOptions).toEqual(formattedSuggestions);
		expect(wrapper.findAll('[data-test="address-option"]')).toHaveLength(5);
		wrapper.findAll('[data-test="address-option"]').at(0).trigger("click");
		// Check if all fields related to address change
		await localVue.nextTick();
		expect(await wrapper.vm.autocompleteSelect).toBeCalled();
		await localVue.nextTick();
		expect(wrapper.find('[data-test="address"]').element.value).toEqual(
			"Carrer Miquel Forteza i Pinya 3, 07007, Coll d'en Rabassa, Palma, Illes Balears, ESP",
		);
		expect(
			wrapper.vm.inputDataList.find((needle) => needle.name === "address")
				.value,
		).toEqual("Carrer Miquel Forteza i Pinya");
		await localVue.nextTick();
		expect(
			wrapper.find('[data-test="residence_country"]').element.value,
		).toEqual("Spain");
		expect(
			wrapper.vm.inputDataList.find(
				(needle) => needle.name === "residence_country",
			).value,
		).toEqual("ESP");
		await localVue.nextTick();
		expect(
			wrapper.vm.inputDataList.find((needle) => needle.name === "postal_code")
				.value,
		).toEqual("07007");

		expect(wrapper.find('[data-test="postal_code"]').element.value).toEqual(
			"07007",
		);
		await localVue.nextTick();
		expect(
			wrapper.vm.inputDataList.find((needle) => needle.name === "CCAA").value,
		).toEqual("04");
		expect(wrapper.find('[data-test="CCAA"]').element.value).toEqual("04");
		await localVue.nextTick();
		
		expect(
			wrapper.vm.inputDataList.find((needle) => needle.name === "province")
				.value,
		).toEqual("07");
		expect(wrapper.find('[data-test="province"]').element.value).toEqual("07");
		expect(wrapper.vm.autocompleteOptions).toEqual([]);
		await localVue.nextTick();
		expect(wrapper.find('[data-test="CCAA"]').isVisible()).toBe(true);
		expect(wrapper.find('[data-test="province"]').isVisible()).toBe(true);

		// If CCAA changes, postal code should be cleared
		wrapper.find('[data-test="CCAA"]').setValue("13");
		await localVue.nextTick();
		expect(wrapper.find('[data-test="postal_code"]').element.value).toEqual("");

		// If postal_code changes, ccaa and province should be filled
		wrapper.find('[data-test="postal_code"]').setValue("07");
		await localVue.nextTick();
		expect(wrapper.find('[data-test="CCAA"]').element.value).toEqual("04");
		expect(wrapper.find('[data-test="province"]').element.value).toEqual("07");

		// If residence_country changes, inputs are cleared
		wrapper.find('[data-test="residence_country"]').setValue("Italia");
		await localVue.nextTick();
		expect(wrapper.find('[data-test="CCAA"]').exists()).toBe(false);
		expect(wrapper.find('[data-test="province"]').exists()).toBe(false);
		await localVue.nextTick();
		expect(
			wrapper.vm.inputDataList.find((needle) => needle.name === "CCAA").value,
		).toEqual(null);
		expect(
			wrapper.vm.inputDataList.find((needle) => needle.name === "province")
				.value,
		).toEqual(null);
	});
});

describe("No residence country without scanned data", () => {
	let store;
	let wrapper;
	let brand;
	let guest;
	let reservations;
	let scan;
	let queryParams;

	beforeEach(async () => {
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getSelectedGuest: () => {
					return {
						address: {
							house_number: null,
							postal_code: null,
							province: null,
							street: null,
						},
						birthday_date: "1974-04-07",
						date_of_expiry: "2050-06-16",
						date_of_issue: "2017-08-16",
						document_number: "99999999R",
						document_subtype: "X",
						document_type: "passport",
						gender: "female",
						height: "1.74m",
						residence_country: null,
						issuing_institution: "SEF-SERV ESTR E FRONTEIRAS",
						name: "Ines",
						nationality: "PRT",
						place_of_birth: "Mação Santarém",
						second_surname: "De Magalhães",
						signature: null,
						surname: "Garção",
					};
				},
			},
		};
		queryParams = {
			...queryParamsModule,
			state: {
				data: {
					manualProcess: true,
				},
			},
		};
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: noResidenceCountryConfig.data,
			},
		};
		scan = scanModule;
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
					queryParams,
					scan,
				},
			});
		};
	});

	it("No residence_country config", async () => {
		Geo.searchForSuggestions = jest.fn(async () =>
			Promise.resolve(formattedSuggestions),
		);
		Geo.searchByText = jest.fn(async () =>
			Promise.resolve(defaultNormalizedSelection),
		);
		// In order to test inputs with debounce
		jest.mock("lodash/debounce", () => jest.fn((fn) => fn));
		wrapper = await mount(ValidateData, {
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});
		await localVue.nextTick();
		//spys
		jest.spyOn(wrapper.vm, "inputChanged");
		jest.spyOn(wrapper.vm.$store, "dispatch");
		jest.spyOn(wrapper.vm, "autocompleteSelect");
		// variables
		const addressInput = wrapper.find('[data-test="address"]');
		const streetNumberInput = wrapper.find('[data-test="street_number"]');
		const addressOptions = wrapper.find('[data-test="address-list"]');
		const continueBtn = wrapper.find('[data-test="validate-data-button"]');

		await addressInput.setValue("miquel forteza y piña");
		await streetNumberInput.setValue("3");

		expect(addressOptions.exists()).toBe(true);
		expect(addressOptions.isVisible()).toBe(false);
		expect(wrapper.find('[data-test="residence_country"]').exists()).toBe(
			false,
		);

		await new Promise((r) => setTimeout(r, 1000));

		expect(wrapper.find('[data-test="CCAA"]').exists()).toBe(false);
		expect(wrapper.find('[data-test="province"]').exists()).toBe(false);

		// Selected option, so autocompleteOption sh=ould run==
		expect(wrapper.vm.autocompleteOptions).toEqual(formattedSuggestions);
		expect(wrapper.findAll('[data-test="address-option"]')).toHaveLength(5);
		wrapper.findAll('[data-test="address-option"]').at(0).trigger("click");
		// Check if all fields related to address change=
		await localVue.nextTick();
		expect(await wrapper.vm.autocompleteSelect).toBeCalled();
		await localVue.nextTick();
		expect(wrapper.find('[data-test="address"]').element.value).toEqual(
			"Carrer Miquel Forteza i Pinya 3, 07007, Coll d'en Rabassa, Palma, Illes Balears, ESP",
		);
		expect(
			wrapper.vm.inputDataList.find((needle) => needle.name === "address")
				.value,
		).toEqual("Carrer Miquel Forteza i Pinya");
		expect(wrapper.find('[data-test="street_number"]').element.value).toEqual(
			"3",
		);
		await localVue.nextTick();
		expect(
			wrapper.vm.inputDataList.find((needle) => needle.name === "postal_code")
				.value,
		).toEqual("07007");

		expect(wrapper.find('[data-test="postal_code"]').element.value).toEqual(
			"07007",
		);
		await localVue.nextTick();
		expect(
			wrapper.vm.inputDataList.find((needle) => needle.name === "CCAA").value,
		).toEqual("04");
		expect(wrapper.find('[data-test="CCAA"]').element.value).toEqual("04");
		await localVue.nextTick();
		expect(
			wrapper.vm.inputDataList.find((needle) => needle.name === "province")
				.value,
		).toEqual("07");
		expect(wrapper.find('[data-test="province"]').element.value).toEqual("07");
		expect(wrapper.vm.autocompleteOptions).toEqual([]);
		await localVue.nextTick();
		expect(wrapper.find('[data-test="CCAA"]').isVisible()).toBe(true);
		expect(wrapper.find('[data-test="province"]').isVisible()).toBe(true);

		// If CCAA changes, postal code should be cleared
		wrapper.find('[data-test="CCAA"]').setValue("13");
		await localVue.nextTick();
		expect(wrapper.find('[data-test="postal_code"]').element.value).toEqual("");

		// If postal_code changes, ccaa and province should be filled
		wrapper.find('[data-test="postal_code"]').setValue("07");
		await localVue.nextTick();
		expect(wrapper.find('[data-test="CCAA"]').element.value).toEqual("04");
		expect(wrapper.find('[data-test="province"]').element.value).toEqual("07");

		// Postal code vaidates if residence_country input is not active
		wrapper.find('[data-test="postal_code"]').setValue("07760");
		await localVue.nextTick();
		await continueBtn.trigger("click");
		expect(wrapper.vm.validationErrors).toEqual([]);
	});
});

describe("Address logic with disabled GEO config", () => {
	let store;
	let wrapper;
	let mockRoute;
	let mockRouter;
	let brand;
	let guest;
	let reservations;
	let scan;
	let queryParams;

	beforeEach(async () => {
		const brandConfig = brandAciConfig.data;
		brandConfig.disable_address_autocomplete = true;
		mockRouter = { push: jest.fn() };
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getSelectedGuest: () => {
					return {
						address: {
							house_number: null,
							postcode: null,
							province: null,
							street: null,
						},
						birthday_date: "1974-04-07",
						date_of_expiry: "2050-06-16",
						date_of_issue: "2017-08-16",
						document_number: "99999999R",
						document_subtype: "X",
						document_type: "passport",
						gender: "female",
						height: "1.74m",
						residence_country: "PRT",
						issuing_institution: "SEF-SERV ESTR E FRONTEIRAS",
						name: "Ines",
						nationality: "PRT",
						place_of_birth: "Mação Santarém",
						second_surname: "De Magalhães",
						signature: null,
						surname: "Garção",
					};
				},
			},
		};
		queryParams = queryParamsModule;
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: brandConfig,
			},
		};
		scan = scanModule;
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
					queryParams,
					scan,
				},
			});
		};
	});

	afterEach(() => {
		jest.resetAllMocks();
	});

	it("If residence_country is not selected, SearchForSuggestions is called without filters", async () => {
		// Mock Geo methods
		Geo.searchForSuggestions = jest.fn(async () =>
			Promise.resolve(formattedSuggestions),
		);
		Geo.searchByText = jest.fn(async () =>
			Promise.resolve(defaultNormalizedSelection),
		);

		wrapper = mount(ValidateData, {
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});
		await localVue.nextTick();
		wrapper.vm.selectedCountry = null;
		wrapper.vm.manageAddressInfo({
			name: "address",
			hasError: "false",
			active: "true",
			required: "true",
			value: "test street",
		});

		expect(Geo.searchForSuggestions).not.toBeCalled();
	});
});

describe("Autocomplete of address related fields", () => {
	let store;
	let wrapper;
	let brand;
	let guest;
	let reservations;
	let scan;
	let queryParams;

	beforeEach(async () => {
		const brandConfig = fillFromHolderConfig.data;
		brandConfig.disable_address_autocomplete = false;
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getSelectedGuest: () => {
					return {
						address: {
							house_number: null,
							postcode: null,
							province: null,
							street: null,
						},
						birthday_date: "1974-04-07",
						date_of_expiry: "2050-06-16",
						date_of_issue: "2017-08-16",
						document_number: "99999999R",
						document_subtype: "X",
						document_type: "passport",
						gender: "female",
						height: "1.74m",
						residence_country: "PRT",
						issuing_institution: "SEF-SERV ESTR E FRONTEIRAS",
						name: "Ines",
						nationality: "PRT",
						place_of_birth: "Mação Santarém",
						second_surname: "De Magalhães",
						signature: null,
						surname: "Garção",
					};
				},
			},
		};
		queryParams = {
			...queryParamsModule,
			state: {
				data: {
					manualProcess: true,
				},
			},
		};
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: brandConfig,
			},
		};
		scan = scanModule;
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
					queryParams,
					scan,
				},
			});
		};
	});

	afterEach(() => {
		jest.resetAllMocks();
	});

	it("If postal code is filled and GEO returns only one option, related fields are autocompleted", async () => {
		// Mock Geo methods

		Geo.searchByText = jest.fn(async () =>
			Promise.resolve(singleItemOption),
		);

		jest.mock("lodash/debounce", () => jest.fn((fn) => fn));

		wrapper = mount(ValidateData, {
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		await localVue.nextTick();
		await localVue.nextTick();
		
		await wrapper.find('[data-test="postal_code"]').setValue("1000");

		await new Promise((r) => setTimeout(r, 1000));
		
		expect(wrapper.find('[data-test="municipality"]').element.value).toEqual(
			"Lisboa",
		);

		expect(wrapper.find('[data-test="region"]').element.value).toEqual(
			"Lisboa",
		);

		expect(wrapper.find('[data-test="subregion"]').element.value).toEqual(
			"Lisboa",
		);

		expect(wrapper.find('[data-test="postal_code"]').element.value).toEqual(
			"1000",
		);

	});
});

describe("Validate data methods", () => {
	let inputs;
	let inputValidation;

	describe("inputValidation", () => {
		it("Input active/inactive with error", () => {
			inputs = [
				{
					hasError: true,
					active: "true",
					required: "true",
					value: "test",
				},
				{
					hasError: true,
					active: "false",
					required: "false",
					value: "",
				},
			];

			inputValidation = ValidateData.methods.inputValidation(inputs);

			expect(inputValidation).toEqual([
				{
					hasError: true,
					active: "true",
					required: "true",
					value: "test",
				},
			]);
		});

		it("Input active with w/wo required value", () => {
			inputs = [
				{
					hasError: "true",
					active: "true",
					required: "true",
					value: "test",
				},
				{
					hasError: false,
					active: "true",
					required: "true",
					value: "",
				},
				{
					hasError: false,
					active: "true",
					required: "true",
					value: undefined,
				},
				{
					hasError: false,
					active: "true",
					required: false,
					value: "",
				},
			];

			inputValidation = ValidateData.methods.inputValidation(inputs);

			expect(inputValidation).toEqual([
				{
					hasError: "true",
					active: "true",
					required: "true",
					value: "test",
				},
				{
					hasError: false,
					active: "true",
					required: "true",
					value: "",
				},
				{
					hasError: false,
					active: "true",
					required: "true",
					value: undefined,
				},
			]);
		});

		it("Input active, required with differente values", () => {
			inputs = [
				{
					hasError: false,
					active: "true",
					required: "true",
					value: "TEST",
				},
				{
					hasError: false,
					active: "true",
					required: "true",
					value: null,
				},
				{
					hasError: false,
					active: "true",
					required: "true",
					value: undefined,
				},
				{
					hasError: false,
					active: "true",
					required: "true",
					value: "",
				},
				{
					hasError: false,
					active: "true",
					required: "true",
					value: "0",
				},
			];

			inputValidation = ValidateData.methods.inputValidation(inputs);

			expect(inputValidation).toEqual([
				{
					hasError: false,
					active: "true",
					required: "true",
					value: null,
				},
				{
					hasError: false,
					active: "true",
					required: "true",
					value: undefined,
				},
				{
					hasError: false,
					active: "true",
					required: "true",
					value: "",
				},
			]);
		});
	});

	describe("getInputsWithMuchDifference", () => {
		let original;
		let changed;
		let getDifferences;

		it("Changing default inputs", () => {
			original = [
				{
					name: "name",
					value: "Name",
				},
				{
					name: "surname",
					value: "surname",
				},
			];

			changed = [
				{
					name: "name",
					value: "Testing",
				},
				{
					name: "surname",
					value: "Potato",
				},
			];

			getDifferences = ValidateData.methods.getInputsWithMuchDifference(
				original,
				changed,
			);
			expect(getDifferences).toEqual([
				[
					{
						name: "name",
						value: "Name",
					},
					{
						name: "name",
						value: "Testing",
					},
				],
				[
					{
						name: "surname",
						value: "surname",
					},
					{
						name: "surname",
						value: "Potato",
					},
				],
			]);
		});

		it("Changing from address with ccaa & province to none", () => {
			original = [
				{
					name: "address",
					value: "fake address",
				},
				{
					name: "province",
					value: "0",
				},
				{
					name: "CCAA",
					value: "0",
				},
			];

			changed = [
				{
					name: "address",
					value: "Avenida del testing",
				},
				{
					name: "province",
					value: "04",
				},
				{
					name: "CCAA",
					value: "08",
				},
			];

			getDifferences = ValidateData.methods.getInputsWithMuchDifference(
				original,
				changed,
			);
			expect(getDifferences).toEqual([
				[
					{
						name: "address",
						value: "fake address",
					},
					{
						name: "address",
						value: "Avenida del testing",
					},
				],
			]);
		});

		it("Changing from non ccaa and province address to address with ccaa and province", () => {
			original = [
				{
					name: "address",
					value: "fake address",
				},
				{
					name: "province",
					value: "04",
				},
				{
					name: "CCAA",
					value: "07",
				},
			];

			changed = [
				{
					name: "address",
					value: "Avenida del testing",
				},
				{
					name: "province",
					value: "0",
				},
				{
					name: "CCAA",
					value: "0",
				},
			];

			getDifferences = ValidateData.methods.getInputsWithMuchDifference(
				original,
				changed,
			);
			expect(getDifferences).toEqual([
				[
					{
						name: "address",
						value: "fake address",
					},
					{
						name: "address",
						value: "Avenida del testing",
					},
				],
			]);
		});

		it("Checking if value is too similar, no difference is given", () => {
			original = [
				{
					name: "address",
					value: "fake address",
				},
				{
					name: "name",
					value: "Pepe",
				},
				{
					name: "surname",
					value: "Viyuela",
				},
			];

			changed = [
				{
					name: "address",
					value: "fake address 2",
				},
				{
					name: "name",
					value: "Pepa",
				},

				{
					name: "surname",
					value: "Villuela",
				},
			];

			getDifferences = ValidateData.methods.getInputsWithMuchDifference(
				original,
				changed,
			);
			expect(getDifferences).toEqual([]);
		});

		it("Checking if one of the values is null, difference is not given", () => {
			original = [
				{
					name: "address",
					value: null,
				},
				{
					name: "name",
					value: "Pepe",
				},
				{
					name: "surname",
					value: "Viyuela",
				},
			];

			changed = [
				{
					name: "address",
					value: "fake address",
				},
				{
					name: "name",
					value: "Testing",
				},

				{
					name: "surname",
					value: "Testing",
				},
			];

			getDifferences = ValidateData.methods.getInputsWithMuchDifference(
				original,
				changed,
			);
			expect(getDifferences).toEqual([
				[
					{
						name: "name",
						value: "Pepe",
					},
					{
						name: "name",
						value: "Testing",
					},
				],
				[
					{
						name: "surname",
						value: "Viyuela",
					},
					{
						name: "surname",
						value: "Testing",
					},
				],
			]);
		});
	});

	describe("getOptionByName", () => {
		let getOptionByName;

		it("Exact or similar values", () => {
			getOptionByName = ValidateData.methods.getOptionByName(ccaaList, "Ceuta");
			expect(getOptionByName).toEqual({ code: "18", value: "Ceuta" });

			getOptionByName = ValidateData.methods.getOptionByName(
				ccaaList,
				"Islas Baleares",
				0.15,
			);
			expect(getOptionByName).toEqual({ code: "04", value: "Illes Balears" });

			getOptionByName = ValidateData.methods.getOptionByName(
				ccaaList,
				"Castilla-laMancha",
			);
			expect(getOptionByName).toEqual({
				code: "08",
				value: "Castilla - La Mancha",
			});

			getOptionByName = ValidateData.methods.getOptionByName(
				ccaaList,
				"Comunidad Valenciana",
			);
			expect(getOptionByName).toEqual({
				code: "10",
				value: "Comunitat Valenciana",
			});
		});

		it("If options are empty, method should return 0", () => {
			getOptionByName = ValidateData.methods.getOptionByName(
				[],
				"Comunidad Valenciana",
			);
			expect(getOptionByName).toEqual("0");
		});

		it("Different error values", () => {
			getOptionByName = ValidateData.methods.getOptionByName(ccaaList, null);
			expect(getOptionByName).toEqual("0");

			getOptionByName = ValidateData.methods.getOptionByName("null", null);
			expect(getOptionByName).toEqual("0");

			getOptionByName = ValidateData.methods.getOptionByName("", null);
			expect(getOptionByName).toEqual("0");

			getOptionByName = ValidateData.methods.getOptionByName(ccaaList, "");
			expect(getOptionByName).toEqual("0");

			getOptionByName = ValidateData.methods.getOptionByName(undefined, null);
			expect(getOptionByName).toEqual("0");

			getOptionByName = ValidateData.methods.getOptionByName(
				undefined,
				undefined,
			);
			expect(getOptionByName).toEqual("0");

			getOptionByName = ValidateData.methods.getOptionByName(null, undefined);
			expect(getOptionByName).toEqual("0");
		});
	});

	describe("manageAddress", () => {
		let manageAddress;
		it("Concatenation of all non emtpy/null values", async () => {
			manageAddress = ValidateData.methods.manageAddress(
				{
					street: "Avda De Madrid, S-n",
					postcode: null,
					province: "Madrid",
				},
				"ESP",
			);
			expect(manageAddress).toBe("Avda De Madrid, S-n, Madrid");
		});

		it("If some values are empty or null, return only filled values", () => {
			manageAddress = ValidateData.methods.manageAddress(
				{
					street: "Avda De Madrid, S-n",
					postcode: null,
					province: "",
				},
				"ESP",
			);
			expect(manageAddress).toBe("Avda De Madrid, S-n");
		});

		it("If no street name, method should return null", () => {
			manageAddress = ValidateData.methods.manageAddress(
				{
					street_name: null,
					postcode: null,
					province: "Madrid",
				},
				"ESP",
			);
			expect(manageAddress).toBe(null);
		});

		it("If all data is null, null should be returned", () => {
			manageAddress = ValidateData.methods.manageAddress(
				{
					street_name: null,
					postcode: null,
					province: null,
				},
				"ESP",
			);
			expect(manageAddress).toBe(null);
		});

		it("If no data is given, method should return null", () => {
			manageAddress = ValidateData.methods.manageAddress(null);
			expect(manageAddress).toBe(null);
		});
	});

	describe("sortItemsByPositionIndex", () => {
		let sortedItems;

		it("Inputs are sorted correctly", async () => {
			sortedItems = ValidateData.methods.sortItemsByPositionIndex([
				{
					position: 3,
					active: "true",
					required: "true",
					name: "name",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 1,
					active: "true",
					required: "true",
					name: "surname",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 2,
					active: "true",
					required: "true",
					name: "second_surname",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
			]);

			expect(sortedItems).toStrictEqual([
				{
					position: 1,
					active: "true",
					required: "true",
					name: "surname",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 2,
					active: "true",
					required: "true",
					name: "second_surname",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 3,
					active: "true",
					required: "true",
					name: "name",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
			]);
		});

		it("Inputs with no position key are sorted to the end of the list", async () => {
			sortedItems = ValidateData.methods.sortItemsByPositionIndex([
				{
					position: 4,
					active: "true",
					required: "true",
					name: "gender",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 3,
					active: "true",
					required: "true",
					name: "name",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					active: "true",
					required: "true",
					name: "surname",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 2,
					active: "true",
					required: "true",
					name: "second_surname",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					active: "true",
					required: "true",
					name: "nationality",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 1,
					active: "true",
					required: "true",
					name: "address",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
			]);

			expect(sortedItems).toStrictEqual([
				{
					position: 1,
					active: "true",
					required: "true",
					name: "address",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 2,
					active: "true",
					required: "true",
					name: "second_surname",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 3,
					active: "true",
					required: "true",
					name: "name",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 4,
					active: "true",
					required: "true",
					name: "gender",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					active: "true",
					required: "true",
					name: "surname",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					active: "true",
					required: "true",
					name: "nationality",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
			]);
		});

		it("Inputs with empty position are sorted to the start of the list", async () => {
			sortedItems = ValidateData.methods.sortItemsByPositionIndex([
				{
					position: 4,
					active: "true",
					required: "true",
					name: "gender",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 3,
					active: "true",
					required: "true",
					name: "name",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: null,
					active: "true",
					required: "true",
					name: "surname",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 2,
					active: "true",
					required: "true",
					name: "second_surname",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: "",
					active: "true",
					required: "true",
					name: "nationality",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 1,
					active: "true",
					required: "true",
					name: "address",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
			]);

			expect(sortedItems).toStrictEqual([
				{
					position: null,
					active: "true",
					required: "true",
					name: "surname",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: "",
					active: "true",
					required: "true",
					name: "nationality",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 1,
					active: "true",
					required: "true",
					name: "address",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 2,
					active: "true",
					required: "true",
					name: "second_surname",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 3,
					active: "true",
					required: "true",
					name: "name",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					position: 4,
					active: "true",
					required: "true",
					name: "gender",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
			]);
		});

		it("Sort function with empty array parameter returns empty array", async () => {
			sortedItems = ValidateData.methods.sortItemsByPositionIndex([]);
			expect(sortedItems).toStrictEqual([]);
		});
	});
	describe("reception mode validate data card behaviour", () => {
		let store;
		let wrapper;
		let brand;
		let guest;
		let reservations;
		let scan;
		let queryParams;
		let app;
		beforeEach(async () => {
			reservations = {
				...receptionReservation,
				state: {
					reservationSelected: receptionReservation,
				},
			};
			guest = {
				...guestModule,
				getters: {
					getSelectedGuest: () => {
						return {
							address: "address example",
							birth_country: "BOL",
							birthday: "1998-02-05",
							city: "Palma",
							document_id: "56746654L",
							document_type: "identity_card",
							date_of_issue: "2015-12-25T00:00:00",
							date_of_expiry: "2025-12-24T00:00:00",
							email: "<EMAIL>",
							first_name: "Alex",
							last_name: "Lopez",
							surname: "Quiroga",
							second_surname: "Marcelo",
							gender: "male",
							holder: true,
							lang: "es",
							pax_type: "AD",
							pms_id: "HUES-744381",
							position: null,
							residence_country: "ES",
							telephone: "612312312",
							validated: false,
							nationality: "ESP",
							province: "Baleares",
							postal_code: "07004",
						};
					},
				},
			};
			queryParams = queryParamsModule;
			brand = {
				...brandModule,
				state: {
					brandId: 76,
					mainColor: "123",
					country: "ES",
					config: defaultConfig.data,
				},
			};
			scan = scanModule;
			app = {
				...appModule,
				getters: {
					isReceptionMode: () => true,
				},
			};
			store = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
						queryParams,
						scan,
						app,
					},
				});
			};
		});
		afterEach(async () => {
			await jest.clearAllMocks();
		});

		it("should mount card with some input data inside", async () => {
			store = new Vuex.Store({
        modules: {
          reservations,
          modal,
          loading,
          brand,
          guest,
          queryParams,
          scan: {
            namespaced: true,
            state: {
              entryPointView: "ValidateData",
            },
            getters: scanModule.getters,
            actions: scanModule.actions,
            mutations: scanModule.mutations,
          },
          app: {
            ...appModule,
            getters: {
              isReceptionMode: () => true,
            },
          },
        },
      });
			wrapper = await mount(ValidateData, {
				mocks: {
					$router: { push: jest.fn() },
				},
				store,
				localVue,
				i18n,
			});

			store.state.scan.entryPointView = "ValidateData";

			expect(wrapper.findComponent(ValidateDataCard).exists()).toBe(true); //Renders card if isReceptionMode is on true
			await wrapper.vm.$nextTick();
			expect(wrapper.findComponent(ValidateDataCard).props("inputs")).toEqual(
				wrapper.vm.originalInputDataList,
			); //Compares input and output from the father and child component
			expect(wrapper.vm.showReceptionCard).toBe(true); //data() showReceptionCard set on true if we in reception mode
			expect(wrapper.findComponent(ValidateDataCard).exists()).toBe(true);
		});

		it("shows no card if isReceptionMode = false", async () => {
			store = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
						queryParams,
						scan,
						app: {
							...appModule,
							getters: {
								isReceptionMode: () => false,
							},
						},
					},
				});
			};
			wrapper = await mount(ValidateData, {
				mocks: {
					$router: { push: jest.fn() },
				},
				store,
				localVue,
				i18n,
			});

			wrapper.vm.$store.state.app.isReceptionMode = false;
			expect(wrapper.findComponent(ValidateDataCard).exists()).toBe(false); // Does not render card if isReceptionMode is false
			await wrapper.vm.$nextTick();
			expect(wrapper.vm.showReceptionCard).toBe(false); // showReceptionCard should be false if isReceptionMode is false
		});
	});
});
