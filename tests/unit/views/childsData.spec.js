import Vuex from "vuex";
import { createLocalVue, shallowMount } from "@vue/test-utils";
import ChildsData from "@/views/ChildsData";
import brandModule from "@/store/brand";
import loading from "@/store/loading";
import reservationsModule from "@/store/reservations";
import modal from "@/store/modal";
import guestModule from "@/store/guest";
import appModule from "@/store/app";
import { i18n } from "@/locales";
import { format } from "date-fns";
import { DEFAULT_FORMAT, EMITTED_VALUE } from "@/mixins/dateFormat.js";
import { defaultConfig } from "../../../mocks/modules/brand/data";
import { selectedReservation } from "../../../mocks/modules/integration/data";
import flushPromises from "flush-promises";
import { Geo } from "@aws-amplify/geo";

const localVue = createLocalVue();
localVue.use(Vuex);

// Get birth date according to the argument passed
function getBirthDate(yearsOld) {
	const checkInDate = new Date(selectedReservation.check_in);
	let birthDate = new Date(checkInDate.setFullYear(checkInDate.getFullYear() - yearsOld));
	birthDate = format(birthDate, EMITTED_VALUE);
	return birthDate;
}

describe("Childs Data Page functionality", () => {
	let store;
	let wrapper;
	let mockRoute;
	let mockRouter;
	let brand;
	let guest;
	let reservations;

	beforeEach(async () => {
		mockRouter = {
			push: jest.fn(() => Promise.resolve()),
		};
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getChildGuests: () => {
					return [
						{
							uuid: "a496f57c-5fd5-40ab-9bee-c3419a8eddaf",
							pms_id: null,
							name: null,
							surname: null,
							second_surname: null,
							full_name: null,
							gender: null,
							birthday_date: null,
							email: null,
							telephone: null,
							holder: false,
							lang: null,
							document_type: null,
							document_number: null,
							date_of_issue: null,
							date_of_expiry: null,
							document_support_number: null,
							address: {
								street: null,
								street_number: null,
								postal_code: null,
								province: null,
								city: null,
								CCAA: null,
								region: undefined,
								subregion: undefined,
								country: null,
							},
							nationality: null,
							position: null,
							residence_country: null,
							validated: false,
							processCompleted: false,
							pax_type: "CH",
							comment: {},
							signature: null,
							documentSignature: null,
							timestampSignature: null,
							documentsSaved: null,
							documents: null,
							identityDocumentsSaved: null,
							selected: false,
							signedDocuments: false,
							kinship: null,
						},
						{
							uuid: "a496f57c-5fd5-40ab-9bee-c3419a8eddag",
							pms_id: null,
							name: null,
							surname: null,
							second_surname: null,
							full_name: null,
							gender: null,
							birthday_date: null,
							email: null,
							telephone: null,
							holder: false,
							lang: null,
							document_type: null,
							document_number: null,
							date_of_issue: null,
							date_of_expiry: null,
							document_support_number: null,
							address: {
								street: null,
								street_number: null,
								postal_code: null,
								province: null,
								city: null,
								CCAA: null,
								region: undefined,
								subregion: undefined,
								country: null,
							},
							nationality: null,
							position: null,
							residence_country: null,
							validated: false,
							processCompleted: false,
							pax_type: "CH",
							comment: {},
							signature: null,
							documentSignature: null,
							timestampSignature: null,
							documentsSaved: null,
							documents: null,
							identityDocumentsSaved: null,
							selected: false,
							signedDocuments: false,
							kinship: null,
						},
					];
				},
			},
		};
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: defaultConfig.data,
			},
		};
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
				},
			});
		};
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it("Continue button is enabled when all data is filled", async () => {
		wrapper = await shallowMount(ChildsData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		await flushPromises();

		const submitButton = wrapper.find('[data-test="submitButton"]');
		expect(submitButton.props()["disabled"]).toBe(true);

		const formInputs = wrapper.vm.formInputs.map((inputs) => {
			return inputs.map((input) => {
				if (input.active === "true") {
					input.value = getBirthDate(1);
				}

				return input;
			});
		});
		await wrapper.setData({
			formInputs,
		});

		expect(submitButton.props()["disabled"]).toBe(false);
		submitButton.trigger("click");
		await flushPromises();

		expect(wrapper.vm.$router.push).toHaveBeenCalledWith({
			name: "Documents",
		});

		wrapper.destroy();
	});

	it("Show modal when continue button is clicked and birthday form is filled with an adult age", async () => {
		wrapper = await shallowMount(ChildsData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		await flushPromises();

		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		const submitButton = wrapper.find('[data-test="submitButton"]');
		expect(submitButton.props()["disabled"]).toBe(true);

		const formInputs = wrapper.vm.formInputs.map((inputs) => {
			return inputs.map((input) => {
				if (input.active === "true") {
					input.value = getBirthDate(21);
				}

				return input;
			});
		});
		await wrapper.setData({
			formInputs,
		});

		expect(submitButton.props()["disabled"]).toBe(false);
		submitButton.trigger("click");
		await flushPromises();

		expect(storeDispatch).toBeCalledWith("modal/VISIBLE", true);

		wrapper.destroy();
	});
});

describe("Childs Data Page with validated guests", () => {
	let store;
	let wrapper;
	let mockRoute;
	let mockRouter;
	let brand;
	let guest;
	let reservations;

	beforeEach(async () => {
		mockRouter = {
			push: jest.fn(() => Promise.resolve()),
		};
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getChildGuests: () => {
					return [
						{
							uuid: "a496f57c-5fd5-40ab-9bee-c3419a8eddaf",
							pms_id: null,
							name: null,
							surname: null,
							second_surname: null,
							full_name: null,
							gender: null,
							birthday_date: "2024-01-01",
							email: null,
							telephone: null,
							holder: false,
							lang: null,
							document_type: null,
							document_number: null,
							date_of_issue: null,
							date_of_expiry: null,
							document_support_number: null,
							address: {
								street: null,
								street_number: null,
								postal_code: null,
								province: null,
								city: null,
								CCAA: null,
								region: undefined,
								subregion: undefined,
								country: null,
							},
							nationality: null,
							position: null,
							residence_country: null,
							validated: true,
							processCompleted: false,
							pax_type: "CH",
							comment: {},
							signature: null,
							documentSignature: null,
							timestampSignature: null,
							documentsSaved: null,
							documents: null,
							identityDocumentsSaved: null,
							selected: false,
							signedDocuments: false,
							kinship: null,
						},
						{
							uuid: "a496f57c-5fd5-40ab-9bee-c3419a8eddag",
							pms_id: null,
							name: null,
							surname: null,
							second_surname: null,
							full_name: null,
							gender: null,
							birthday_date: "2020-05-01",
							email: null,
							telephone: null,
							holder: false,
							lang: null,
							document_type: null,
							document_number: null,
							date_of_issue: null,
							date_of_expiry: null,
							document_support_number: null,
							address: {
								street: null,
								street_number: null,
								postal_code: null,
								province: null,
								city: null,
								CCAA: null,
								region: undefined,
								subregion: undefined,
								country: null,
							},
							nationality: null,
							position: null,
							residence_country: null,
							validated: true,
							processCompleted: false,
							pax_type: "CH",
							comment: {},
							signature: null,
							documentSignature: null,
							timestampSignature: null,
							documentsSaved: null,
							documents: null,
							identityDocumentsSaved: null,
							selected: false,
							signedDocuments: false,
							kinship: null,
						},
					];
				},
			},
		};
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: defaultConfig.data,
			},
		};
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
				},
			});
		};
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it("Redirect to Document page if all childs are validated", async () => {
		wrapper = await shallowMount(ChildsData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		await flushPromises();

		expect(wrapper.vm.$router.push).toHaveBeenCalledWith({
			name: "Documents",
		});

		wrapper.destroy();
	});

	describe("Holder data prefill functionality", () => {
		let store;
		let wrapper;
		let mockRouter;
		let brand;
		let guest;
		let reservations;

		beforeEach(async () => {
			mockRouter = {
				push: jest.fn(() => Promise.resolve()),
			};
			reservations = {
				...reservationsModule,
				state: {
					reservationSelected: selectedReservation,
				},
			};
			guest = {
				...guestModule,
				getters: {
					getChildGuests: () => {
						return [
							{
								uuid: "a496f57c-5fd5-40ab-9bee-c3419a8eddaf",
								pms_id: null,
								name: null,
								surname: null,
								second_surname: null,
								full_name: null,
								gender: null,
								birthday_date: null,
								email: null,
								telephone: null,
								holder: false,
								lang: null,
								document_type: null,
								document_number: null,
								date_of_issue: null,
								date_of_expiry: null,
								document_support_number: null,
								address: {
									street: null,
									street_number: null,
									postal_code: null,
									province: null,
									city: null,
									CCAA: null,
									region: undefined,
									subregion: undefined,
									country: null,
								},
								nationality: null,
								position: null,
								residence_country: null,
								validated: false,
								processCompleted: false,
								pax_type: "CH",
								comment: {},
								signature: null,
								documentSignature: null,
								timestampSignature: null,
								documentsSaved: null,
								documents: null,
								identityDocumentsSaved: null,
								selected: false,
								signedDocuments: false,
								kinship: null,
							},
						];
					},
					getHolderGuest: () => {
						return {
							uuid: "holder-uuid",
							pms_id: "holder-123",
							name: "John",
							surname: "Doe",
							email: "<EMAIL>",
							telephone: {
								value: "123456789",
								countryCode: "ES",
								dialCode: "+34"
							},
							address: {
								street: "Main Street",
								street_number: "123",
								postal_code: "12345",
								city: "Madrid",
								province: "Madrid",
								CCAA: "Madrid",
								region: "Madrid",
								subregion: "Centro"
							},
							nationality: "ES",
							holder: true
						};
					},
				},
			};
		});

		afterEach(() => {
			jest.clearAllMocks();
		});

		it("Should prefill child form inputs with holder data when fill_from_holder is true", async () => {
			brand = {
				...brandModule,
				state: {
					brandId: 1,
					mainColor: "123",
					country: "ES",
					config: {
						...defaultConfig.data,
						identification: {
							...defaultConfig.data.identification,
							child_form: [
								[
									{
										name: "address",
										type: "text",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									},
									{
										name: "postal_code",
										type: "text",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									},
									{
										name: "municipality",
										type: "text",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									}
								]
							]
						}
					}
				},
			};

			const storeWithConfig = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
					},
				});
			};

			wrapper = await shallowMount(ChildsData, {
				mocks: {
					$router: mockRouter,
				},
				store: storeWithConfig,
				localVue,
				i18n,
				attachTo: document.body,
			});

			await flushPromises();

			// Check that the form inputs have been prefilled with holder data
			const formInputs = wrapper.vm.formInputs[0];

			const addressInput = formInputs.find(input => input.name === "address");
			expect(addressInput.value).toBe("Main Street");

			const postalCodeInput = formInputs.find(input => input.name === "postal_code");
			expect(postalCodeInput.value).toBe("12345");

			const municipalityInput = formInputs.find(input => input.name === "municipality");
			expect(municipalityInput.value).toBe("Madrid");

			// Check that street number is also prefilled
			expect(wrapper.vm.streetNumbers[0]).toBe("123");

			wrapper.destroy();
		});

		it("Should NOT prefill inputs for validated guests", async () => {
			brand = {
				...brandModule,
				state: {
					brandId: 1,
					mainColor: "123",
					country: "ES",
					config: {
						...defaultConfig.data,
						identification: {
							...defaultConfig.data.identification,
							child_form: [
								[
									{
										name: "address",
										type: "text",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									},
									{
										name: "postal_code",
										type: "text",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									}
								]
							]
						}
					}
				},
			};

			// Mock a validated child guest
			guest = {
				...guestModule,
				getters: {
					...guestModule.getters,
					getChildGuests: () => [
						{
							name: "Child Name",
							surname: "Child Surname",
							birthday_date: "2015-01-01",
							nationality: "ES",
							validated: true, // This child is already validated
							address: {
								street: "Existing Street",
								postal_code: "54321"
							}
						}
					],
					getHolderGuest: () => {
						return {
							name: "Holder Name",
							surname: "Holder Surname",
							birthday_date: "1990-01-01",
							telephone: {
								value: "123456789",
								countryCode: "ES",
								dialCode: "+34"
							},
							address: {
								street: "Main Street",
								street_number: "123",
								postal_code: "12345",
								city: "Madrid",
								province: "Madrid",
								CCAA: "Madrid",
								region: "Madrid",
								subregion: "Centro"
							},
							nationality: "ES",
							holder: true
						};
					},
				},
			};

			const storeWithConfig = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
					},
				});
			};

			wrapper = await shallowMount(ChildsData, {
				mocks: {
					$router: mockRouter,
				},
				store: storeWithConfig,
				localVue,
				i18n,
				attachTo: document.body,
			});

			await flushPromises();

			// Check that the form inputs have NOT been prefilled with holder data
			const formInputs = wrapper.vm.formInputs[0];

			const addressInput = formInputs.find(input => input.name === "address");
			// Should keep existing child data, not be prefilled with holder data
			expect(addressInput.value).toBe("Existing Street");
			expect(addressInput.disabled).toBe(true); // Should be disabled for validated guests

			const postalCodeInput = formInputs.find(input => input.name === "postal_code");
			// Should keep existing child data, not be prefilled with holder data
			expect(postalCodeInput.value).toBe("54321");
			expect(postalCodeInput.disabled).toBe(true); // Should be disabled for validated guests

			// Check that the isChildValidated computed property works correctly
			expect(wrapper.vm.isChildValidated(0)).toBe(true); // First child is validated

			wrapper.destroy();
		});
	});

	describe("Reception mode functionality", () => {
		let store;
		let wrapper;
		let mockRouter;
		let brand;
		let guest;
		let reservations;
		let app;

		beforeEach(async () => {
			mockRouter = {
				push: jest.fn(() => Promise.resolve()),
			};
			reservations = {
				...reservationsModule,
				state: {
					reservationSelected: selectedReservation,
				},
			};
			// Mock app module with reception mode enabled
			app = {
				...appModule,
				getters: {
					isReceptionMode: () => true, // Enable reception mode
					getPreviousComponent: () => "",
					isDemoMode: () => false,
				},
			};
		});

		afterEach(() => {
			jest.clearAllMocks();
		});

		it("Should prefill validated child inputs with holder data in reception mode", async () => {
			guest = {
				...guestModule,
				getters: {
					getChildGuests: () => [
						{
							uuid: "child-uuid",
							name: "Child Name",
							surname: "Child Surname",
							birthday_date: "2015-01-01",
							nationality: "ES",
							validated: true, // Child is validated
							address: {
								street: null, // Start with empty values so holder data can fill them
								street_number: null,
								postal_code: null,
								city: null
							}
						}
					],
					getHolderGuest: () => ({
						name: "Holder Name",
						surname: "Holder Surname",
						email: "<EMAIL>",
						telephone: {
							value: "987654321",
							countryCode: "ES",
							dialCode: "+34"
						},
						address: {
							street: "Holder Street",
							street_number: "456",
							postal_code: "98765",
							city: "Holder City"
						},
						nationality: "FR",
						holder: true
					}),
				},
			};

			brand = {
				...brandModule,
				state: {
					brandId: 1,
					mainColor: "123",
					country: "ES",
					config: {
						...defaultConfig.data,
						identification: {
							...defaultConfig.data.identification,
							child_form: [
								[
									{
										name: "address",
										type: "text",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									},
									{
										name: "postal_code",
										type: "text",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									},
									{
										name: "municipality",
										type: "text",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									},
									{
										name: "email",
										type: "email",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									},
									{
										name: "telephone",
										type: "telephone",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									},
									{
										name: "nationality",
										type: "select",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									}
								]
							]
						}
					}
				},
			};

			const storeWithReceptionMode = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
						app, // Include app module with reception mode
					},
				});
			};

			wrapper = await shallowMount(ChildsData, {
				mocks: {
					$router: mockRouter,
				},
				store: storeWithReceptionMode,
				localVue,
				i18n,
				attachTo: document.body,
			});

			await flushPromises();

			// In reception mode, even validated children should get prefilled with holder data
			const formInputs = wrapper.vm.formInputs[0];

			const addressInput = formInputs.find(input => input.name === "address");
			expect(addressInput.value).toBe("Holder Street");

			const postalCodeInput = formInputs.find(input => input.name === "postal_code");
			expect(postalCodeInput.value).toBe("98765");

			const municipalityInput = formInputs.find(input => input.name === "municipality");
			expect(municipalityInput.value).toBe("Holder City");

			const emailInput = formInputs.find(input => input.name === "email");
			expect(emailInput.value).toBe("<EMAIL>");

			const telephoneInput = formInputs.find(input => input.name === "telephone");
			expect(telephoneInput.value).toBe("987654321");

			const nationalityInput = formInputs.find(input => input.name === "nationality");
			expect(nationalityInput.value).toBe("FR");

			// Check that street number is also prefilled
			expect(wrapper.vm.streetNumbers[0]).toBe("456");

			wrapper.destroy();
		});

		it("Should NOT redirect to Documents page when form is completed in reception mode", async () => {
			guest = {
				...guestModule,
				getters: {
					getChildGuests: () => [
						{
							uuid: "child-uuid",
							name: "Child Name",
							surname: "Child Surname",
							birthday_date: "2015-01-01",
							nationality: "ES",
							validated: false,
							address: {
								street: "Child Street",
								street_number: "123",
								postal_code: "12345",
								city: "Child City"
							}
						}
					],
					getHolderGuest: () => ({
						name: "Holder Name",
						surname: "Holder Surname",
						holder: true
					}),
				},
			};

			brand = {
				...brandModule,
				state: {
					brandId: 1,
					mainColor: "123",
					country: "ES",
					config: {
						...defaultConfig.data,
						identification: {
							...defaultConfig.data.identification,
							child_form: [
								[
									{
										name: "birthday",
										type: "date",
										active: "true",
										required: "true",
										value: "2015-01-01" // Pre-filled to make form complete
									}
								]
							]
						}
					}
				},
			};

			const storeWithReceptionMode = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
						app, // Include app module with reception mode enabled
					},
				});
			};

			wrapper = await shallowMount(ChildsData, {
				mocks: {
					$router: mockRouter,
				},
				store: storeWithReceptionMode,
				localVue,
				i18n,
				attachTo: document.body,
			});

			await flushPromises();

			// In reception mode, should NOT auto-redirect to Documents page even when form is complete
			expect(wrapper.vm.$router.push).not.toHaveBeenCalledWith({
				name: "Documents",
			});

			wrapper.destroy();
		});
	});

	describe("GEO address autocomplete functionality", () => {
		let store;
		let wrapper;
		let mockRouter;
		let brand;
		let guest;
		let reservations;

		beforeEach(async () => {
			mockRouter = {
				push: jest.fn(() => Promise.resolve()),
			};
			reservations = {
				...reservationsModule,
				state: {
					reservationSelected: selectedReservation,
				},
			};
			guest = {
				...guestModule,
				getters: {
					getChildGuests: () => [
						{
							uuid: "child-uuid",
							name: null,
							surname: null,
							birthday_date: null,
							nationality: null,
							validated: false,
							address: {
								street: null,
								street_number: null,
								postal_code: null,
								city: null
							}
						}
					],
					getHolderGuest: () => null,
				},
			};
			brand = {
				...brandModule,
				state: {
					brandId: 1,
					mainColor: "123",
					country: "ES",
					config: {
						...defaultConfig.data,
						disable_address_autocomplete: false, // Enable address autocomplete
						identification: {
							...defaultConfig.data.identification,
							child_form: [
								[
									{
										name: "address",
										type: "text",
										active: "true",
										required: "false",
										value: ""
									},
									{
										name: "postal_code",
										type: "text",
										active: "true",
										required: "false",
										value: ""
									},
									{
										name: "municipality",
										type: "text",
										active: "true",
										required: "false",
										value: ""
									}
								]
							]
						}
					}
				},
			};
			store = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
					},
				});
			};
		});

		afterEach(() => {
			jest.clearAllMocks();
		});

		it("Should fill street_number, postal_code and municipality when GEO address is selected", async () => {
			// Mock Geo.searchByText to return location data
			const mockLocationData = [
				{
					street: "Carrer Miquel Forteza i Pinya",
					addressNumber: "3",
					municipality: "Palma",
					postalCode: "07007",
					region: "Illes Balears",
					country: "ESP"
				}
			];
			Geo.searchByText = jest.fn().mockResolvedValue(mockLocationData);

			wrapper = await shallowMount(ChildsData, {
				mocks: {
					$router: mockRouter,
				},
				store,
				localVue,
				i18n,
				attachTo: document.body,
			});

			await flushPromises();

			// Simulate GEO address selection
			const addressInput = wrapper.vm.formInputs[0].find(input => input.name === "address");

			// Mock the autocomplete selection event with AWS GEO data
			const mockEvent = {
				value: "Carrer Miquel Forteza i Pinya 3, 07007, Coll d'en Rabassa, Palma, Illes Balears, ESP",
				selectedOption: { placeId: "some-place-id" },
				event: true
			};

			await wrapper.vm.autocompleteSelect(mockEvent, addressInput);

			// Check that Geo.searchByText was called
			expect(Geo.searchByText).toHaveBeenCalledWith(mockEvent.value);

			// Check that related fields were updated
			expect(wrapper.vm.streetNumbers[0]).toBe("3");

			const formInputs = wrapper.vm.formInputs[0];
			const postalCodeInput = formInputs.find(input => input.name === "postal_code");
			expect(postalCodeInput.value).toBe("07007");

			const municipalityInput = formInputs.find(input => input.name === "municipality");
			expect(municipalityInput.value).toBe("Palma");

			// Check that the address input itself was updated with just the street
			expect(addressInput.value).toBe("Carrer Miquel Forteza i Pinya");

			wrapper.destroy();
		});

		it("Should handle GEO address selection gracefully when no location data is returned", async () => {
			// Mock Geo.searchByText to return empty array
			Geo.searchByText = jest.fn().mockResolvedValue([]);

			wrapper = await shallowMount(ChildsData, {
				mocks: {
					$router: mockRouter,
				},
				store,
				localVue,
				i18n,
				attachTo: document.body,
			});

			await flushPromises();

			// Simulate GEO address selection with no results
			const addressInput = wrapper.vm.formInputs[0].find(input => input.name === "address");

			const mockEvent = {
				value: "Non-existent address",
				selectedOption: { placeId: "some-place-id" },
				event: true
			};

			await wrapper.vm.autocompleteSelect(mockEvent, addressInput);

			// Check that Geo.searchByText was called
			expect(Geo.searchByText).toHaveBeenCalledWith(mockEvent.value);

			// Check that no fields were updated when no location data is returned
			expect(wrapper.vm.streetNumbers[0]).toBeNull();

			const formInputs = wrapper.vm.formInputs[0];
			const postalCodeInput = formInputs.find(input => input.name === "postal_code");
			expect(postalCodeInput.value).toBe("");

			const municipalityInput = formInputs.find(input => input.name === "municipality");
			expect(municipalityInput.value).toBe("");

			wrapper.destroy();
		});

		it("Should not call GEO services when address autocomplete is disabled", async () => {
			// Update brand config to disable address autocomplete
			brand.state.config.disable_address_autocomplete = true;

			const storeWithDisabledGeo = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
					},
				});
			};

			Geo.searchForSuggestions = jest.fn();

			wrapper = await shallowMount(ChildsData, {
				mocks: {
					$router: mockRouter,
				},
				store: storeWithDisabledGeo,
				localVue,
				i18n,
				attachTo: document.body,
			});

			await flushPromises();

			// Simulate address input change
			const addressInput = wrapper.vm.formInputs[0].find(input => input.name === "address");

			await wrapper.vm.handleInputChanged({ value: "Test address input" }, addressInput);

			// Check that GEO services were not called when disabled
			expect(Geo.searchForSuggestions).not.toHaveBeenCalled();

			wrapper.destroy();
		});
	});
});
