import Vuex from "vuex";
import { shallowMount, createLocalVue } from "@vue/test-utils";
import SendDocuments from "@/views/SendDocuments.vue";
import { i18n } from "@/locales";
import guestModule from "@/store/guest.js";
import brandModule from "@/store/brand.js";
import loadingModule from "@/store/loading.js";
import modalModule from "@/store/modal.js";
import { defaultConfig } from "../../../mocks/modules/brand/data";

const localVue = createLocalVue();
localVue.use(Vuex);

// Mock the repository
jest.mock("@/repository/repositoryFactory", () => ({
  get: () => ({
    validateEmail: jest.fn(() => Promise.resolve({ valid: true }))
  })
}));

describe("SendDocuments Component - documentsEmail functionality", () => {
  let store;
  let wrapper;

  beforeEach(() => {
    store = new Vuex.Store({
      modules: {
        brand: {
          ...brandModule,
          state: {
            ...brandModule.state,
            brandId: "test-brand",
            config: {
              ...defaultConfig.data,
              max_attempts_email_validation: 3,
              children_sign_documents: false
            }
          }
        },
        guest: {
          ...guestModule,
          state: {
            ...guestModule.state,
            list: [
              {
                selected: true,
                name: "John",
                surname: "Doe",
                email: "<EMAIL>",
                documentsEmail: null,
                pax_type: "AD"
              }
            ],
            emailValidationAttempt: 0
          }
        },
        loading: loadingModule,
        modal: modalModule
      }
    });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
    jest.clearAllMocks();
  });

  it("should initialize with main email when documentsEmail is null", async () => {
    wrapper = shallowMount(SendDocuments, {
      store,
      localVue,
      i18n,
      mocks: {
        $router: { push: jest.fn() }
      }
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.vm.email).toBe("<EMAIL>");
  });

  it("should initialize with documentsEmail when available", async () => {
    // Set documentsEmail in store
    store.state.guest.list[0].documentsEmail = "<EMAIL>";

    wrapper = shallowMount(SendDocuments, {
      store,
      localVue,
      i18n,
      mocks: {
        $router: { push: jest.fn() }
      }
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.vm.email).toBe("<EMAIL>");
  });

  it("should call UPDATE_DOCUMENTS_EMAIL when email is validated", async () => {
    const updateDocumentsEmailSpy = jest.spyOn(store, 'dispatch');
    
    wrapper = shallowMount(SendDocuments, {
      store,
      localVue,
      i18n,
      mocks: {
        $router: { push: jest.fn() }
      }
    });

    wrapper.vm.email = "<EMAIL>";
    await wrapper.vm.validateEmail();

    expect(updateDocumentsEmailSpy).toHaveBeenCalledWith(
      'guest/UPDATE_DOCUMENTS_EMAIL',
      '<EMAIL>'
    );
  });

  it("should not affect main email when updating documentsEmail", async () => {
    const originalMainEmail = store.state.guest.list[0].email;
    
    wrapper = shallowMount(SendDocuments, {
      store,
      localVue,
      i18n,
      mocks: {
        $router: { push: jest.fn() }
      }
    });

    wrapper.vm.email = "<EMAIL>";
    await wrapper.vm.validateEmail();

    // Main email should remain unchanged
    expect(store.state.guest.list[0].email).toBe(originalMainEmail);
    // documentsEmail should be updated
    expect(store.state.guest.list[0].documentsEmail).toBe("<EMAIL>");
  });

  it("should update email input when inputChange is called", () => {
    wrapper = shallowMount(SendDocuments, {
      store,
      localVue,
      i18n,
      mocks: {
        $router: { push: jest.fn() }
      }
    });

    const mockEvent = {
      value: "<EMAIL>",
      error: false
    };

    wrapper.vm.inputChange(mockEvent);

    expect(wrapper.vm.email).toBe("<EMAIL>");
    expect(wrapper.vm.disableButton).toBe(false);
  });
});
