import Vuex from "vuex";
import VueRouter from "vue-router";
import { shallowMount, createLocalVue } from "@vue/test-utils";
import Confirmation from "@/views/Confirmation.vue";
import brandModule from "@/store/brand";
import loading from "@/store/loading";
import reservationModule from "@/store/reservations";
import guestModule from "@/store/guest";
import { guestData } from "../../../mocks/data/guestData";
import scanModule from "@/store/scan";
import { defaultConfig } from "../../../mocks/modules/brand/data";
import {
	selectedReservation,
	finishedReservation,
	redirectUrlResponse,
} from "../../../mocks/modules/integration/data";
import queryParamsModule from "@/store/queryParams";
import router from "@/router";
import { i18n } from "@/locales";
import repository from "@/repository/repositoryFactory";
import modal from "@/store/modal";
import redirectMixin from "@/mixins/redirect";
import flushPromises from "flush-promises";
import app from "@/store/app";
import { reservationLogicError } from "../../../mocks/modules/integration/data";
import { nextTick } from "vue";
const localVue = createLocalVue();
localVue.use(VueRouter);
localVue.use(Vuex);
const api = repository.get("integration");

describe("Confirmation Page", () => {
	let store;
	beforeEach(() => {
		const api = repository.get("integration");
		const AutoCheckinApi = repository.get("checkin");

		store = new Vuex.Store({
			modules: {
				queryParams: queryParamsModule,
				brand: {
					...brandModule,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							...defaultConfig,
						},
					},
				},
				scan: scanModule,
				reservations: {
					...reservationModule,
					state: {
						reservationSelected: null,
						reservationCheckinCompleted: false,
					},
				},
				guest: guestModule,
				loading,
				modal,
			},
		});

		api.sendGuestToPMS = jest.fn();
		AutoCheckinApi.getCustomizedText = jest.fn(() =>
			Promise.resolve({
				data: [
					{
						translations: [
							{
								text: "test",
							},
						],
					},
				],
			}),
		);
	});

	it("If theres no reservationSelected, router should send you to error page", async () => {
		const spyPush = jest.spyOn(router, "push");
		shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});
		expect(spyPush).toBeCalledWith({
			name: "Error",
		});
	});
});

describe("Confirmation page logic", () => {
	let store;
	beforeEach(() => {
		const api = repository.get("integration");
		const AutoCheckinApi = repository.get("checkin");

		store = new Vuex.Store({
			modules: {
				queryParams: queryParamsModule,
				brand: {
					...brandModule,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							...defaultConfig,
							custom_confirmation_text: true,
							show_qr_code: true,
							show_modal_in_confirmation_page: true,
						},
					},
				},
				scan: scanModule,
				reservations: {
					...reservationModule,
					state: {
						reservationSelected: selectedReservation,
						reservationCheckinCompleted: false,
					},
				},
				guest: guestModule,
				loading,
				modal,
			},
		});

		api.sendGuestToPMS = jest.fn();
		AutoCheckinApi.getCustomizedText = AutoCheckinApi.getCustomizedText =
			jest.fn(() =>
				Promise.resolve({
					data: [
						{
							translations: [
								{
									text: "test",
								},
							],
						},
					],
				}),
			);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it("Modal shows when confirmation view is loaded and config has enabled show_modal_in_confirmation_page", async () => {
		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});

		const modal = wrapper.find('[data-test="confirmModal"]');
		await new Promise(process.nextTick);
		expect(modal.isVisible()).toBe(true);
	});

	it("Modal does NOT show when confirmation view is loaded and config has NOT enabled show_modal_in_confirmation_page", async () => {
		store.state.brand.config.show_modal_in_confirmation_page = false;

		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});

		await new Promise(process.nextTick);
		expect(wrapper.find('[data-test="confirmModal"]').exists()).toBe(false);
	});

	it("Phone is parsed correctly", async () => {
		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});

		const spanishPhone = await wrapper.vm.getFormattedPhone({
			value: "666666666",
			dialCode: "+34",
		});
		expect(spanishPhone).toEqual("+34666666666");

		const spanishPhoneBadFormat = await wrapper.vm.getFormattedPhone({
			value: "+34666666666",
			dialCode: "+34",
		});
		expect(spanishPhoneBadFormat).toEqual("+34666666666");

		const spanishPhoneWithUndefinedDialCode =
			await wrapper.vm.getFormattedPhone({
				value: "+34666666666",
				dialCode: "undefined",
			});
		expect(spanishPhoneWithUndefinedDialCode).toEqual("+34666666666");

		const spanishPhoneWithPlusUndefinedDialCode =
			await wrapper.vm.getFormattedPhone({
				value: "+34666666666",
				dialCode: "+undefined",
			});
		expect(spanishPhoneWithPlusUndefinedDialCode).toEqual("+34666666666");

		const spanishPhoneWithExtraPlusDialCode =
			await wrapper.vm.getFormattedPhone({
				value: "+34666666666",
				dialCode: "++undefined",
			});
		expect(spanishPhoneWithExtraPlusDialCode).toEqual("+34666666666");

		const andorraPhone = await wrapper.vm.getFormattedPhone({
			value: "666666666",
			dialCode: "+376",
		});
		expect(andorraPhone).toEqual("+376666666666");

		const noDialCodePhone = await wrapper.vm.getFormattedPhone({
			value: "666666666",
		});
		expect(noDialCodePhone).toEqual(null);

		const noValuePhone = await wrapper.vm.getFormattedPhone({
			dialCode: "+34",
		});
		expect(noValuePhone).toEqual(null);

		const nullPhone = await wrapper.vm.getFormattedPhone(null);
		expect(nullPhone).toEqual(null);
	});

	it("If config has custom_confirmation_text and show_qr_code, getCustomConfirmationText and createQrCode methods are called and view renders correctly", async () => {
		const getCustomConfirmationText = jest.spyOn(
			Confirmation.methods,
			"getCustomConfirmationText",
		);
		const createQrCode = jest.spyOn(Confirmation.methods, "createQrCode");
		await flushPromises;

		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});

		expect(getCustomConfirmationText).toBeCalled();
		await new Promise(process.nextTick);
		expect(createQrCode).toBeCalled();

		expect(wrapper.find('[data-test="qr-code"]').exists()).toBe(true);
		expect(wrapper.find('[data-test="richConfirmationText"]').exists()).toBe(
			true,
		);
		expect(wrapper.find('[data-test="defaultConfirmationText"]').exists()).toBe(
			false,
		);
	});

	it("If config does NOT have custom_confirmation_text, getCustomConfirmationText method is NOT called and view renders correctly", async () => {
		const getCustomConfirmationText = jest.spyOn(
			Confirmation.methods,
			"getCustomConfirmationText",
		);
		const createQrCode = jest.spyOn(Confirmation.methods, "createQrCode");
		store = new Vuex.Store({
			modules: {
				queryParams: queryParamsModule,
				brand: {
					...brandModule,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							...defaultConfig,
							custom_confirmation_text: false,
							show_qr_code: true,
						},
					},
				},
				scan: scanModule,
				reservations: {
					...reservationModule,
					state: {
						reservationSelected: selectedReservation,
						reservationCheckinCompleted: false,
					},
				},
				guest: guestModule,
				loading,
			},
		});

		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});

		expect(getCustomConfirmationText).not.toBeCalled();
		await new Promise(process.nextTick);
		expect(createQrCode).toBeCalled();

		expect(wrapper.find('[data-test="qr-code"]').exists()).toBe(true);
		expect(wrapper.find('[data-test="richConfirmationText"]').exists()).toBe(
			false,
		);
		expect(wrapper.find('[data-test="defaultConfirmationText"]').exists()).toBe(
			true,
		);
	});

	it("If config does have custom_confirmation_text, but not show_qr_code, createQrCode method is NOT called and view renders correctly", async () => {
		const getCustomConfirmationText = jest.spyOn(
			Confirmation.methods,
			"getCustomConfirmationText",
		);
		const createQrCode = jest.spyOn(Confirmation.methods, "createQrCode");
		store = new Vuex.Store({
			modules: {
				queryParams: queryParamsModule,
				brand: {
					...brandModule,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							...defaultConfig,
							custom_confirmation_text: true,
							show_qr_code: false,
						},
					},
				},
				scan: scanModule,
				reservations: {
					...reservationModule,
					state: {
						reservationSelected: selectedReservation,
						reservationCheckinCompleted: false,
					},
				},
				guest: guestModule,
				loading,
			},
		});

		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});

		expect(getCustomConfirmationText).toBeCalled();
		await new Promise(process.nextTick);
		expect(createQrCode).not.toBeCalled();

		expect(wrapper.find('[data-test="qr-code"]').exists()).toBe(false);
		expect(wrapper.find('[data-test="richConfirmationText"]').exists()).toBe(
			true,
		);
		expect(wrapper.find('[data-test="defaultConfirmationText"]').exists()).toBe(
			false,
		);
	});

	it("If config does NOT have custom_confirmation_text, NOR show_qr_code, getCustomConfirmationText and createQrCode methods are NOT called and view renders correctly", async () => {
		const getCustomConfirmationText = jest.spyOn(
			Confirmation.methods,
			"getCustomConfirmationText",
		);
		const createQrCode = jest.spyOn(Confirmation.methods, "createQrCode");
		store = new Vuex.Store({
			modules: {
				queryParams: queryParamsModule,
				brand: {
					...brandModule,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							...defaultConfig,
							custom_confirmation_text: false,
							show_qr_code: false,
						},
					},
				},
				scan: scanModule,
				reservations: {
					...reservationModule,
					state: {
						reservationSelected: selectedReservation,
						reservationCheckinCompleted: false,
					},
				},
				guest: guestModule,
				loading,
			},
		});

		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});

		expect(getCustomConfirmationText).not.toBeCalled();
		await new Promise(process.nextTick);
		expect(createQrCode).not.toBeCalled();

		expect(wrapper.find('[data-test="qr-title"]').exists()).toBe(false);
		expect(wrapper.find('[data-test="qr-code"]').exists()).toBe(false);
		expect(wrapper.find('[data-test="richConfirmationText"]').exists()).toBe(
			false,
		);
		expect(wrapper.find('[data-test="defaultConfirmationText"]').exists()).toBe(
			false,
		);
	});

	it("Should fill the face field in the formatted guest", () => {
		const guest = {};
		const faceValue = store.state.scan.face;

		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});

		const formattedGuest = wrapper.vm.getFormattedGuest(guest);
		expect([null, undefined]).toContain(formattedGuest.face);
	});

	it("Should use main email for PMS in getFormattedGuest, not documentsEmail", () => {
		const guest = {
			email: "<EMAIL>",
			documentsEmail: "<EMAIL>",
			name: "John",
			surname: "Doe"
		};

		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});

		const formattedGuest = wrapper.vm.getFormattedGuest(guest);
		expect(formattedGuest.email).toBe("<EMAIL>");
		expect(formattedGuest.email).not.toBe("<EMAIL>");
	});

	it("Should use documentsEmail for document sending when available", () => {
		const guest = {
			email: "<EMAIL>",
			documentsEmail: "<EMAIL>",
			name: "John",
			surname: "Doe",
			sendDocuments: true,
			documentsSaved: {
				files: [{ fileName: "test.pdf" }],
				key: "test-key"
			}
		};

		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});

		// Mock the sendDocuments method to test email selection
		const sendDocumentsSpy = jest.spyOn(wrapper.vm, 'sendDocuments');
		wrapper.vm.sendDocuments = jest.fn().mockImplementation(() => {
			const body = {
				user: {
					email: guest.documentsEmail || guest.email
				}
			};
			return Promise.resolve(body);
		});

		wrapper.vm.sendDocuments();
		expect(sendDocumentsSpy).toHaveBeenCalled();
	});

	it("Should fallback to main email for document sending when documentsEmail is null", () => {
		const guest = {
			email: "<EMAIL>",
			documentsEmail: null,
			name: "John",
			surname: "Doe",
			sendDocuments: true
		};

		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});

		// Test the email fallback logic
		const emailForDocuments = guest.documentsEmail || guest.email;
		expect(emailForDocuments).toBe("<EMAIL>");
	});

	it("Should keep main email and documentsEmail separate in formatted guest", () => {
		const guest = {
			email: "<EMAIL>",
			documentsEmail: "<EMAIL>",
			name: "John",
			surname: "Doe"
		};

		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});

		const formattedGuest = wrapper.vm.getFormattedGuest(guest);

		// Formatted guest should only have main email for PMS
		expect(formattedGuest.email).toBe("<EMAIL>");
		expect(formattedGuest).not.toHaveProperty('documentsEmail');
	});

	it("Should fill the face field only if guest is selected", () => {
		const wrapper = shallowMount(Confirmation, { store, i18n, router });

		const selectedGuest = { selected: true };
		const formattedSelected = wrapper.vm.getFormattedGuest(selectedGuest);
		expect(formattedSelected.face).toBe(store.state.scan.face || null);

		const notSelectedAdult = { pax_type: "AD", selected: false };
		const formattedNotSelectedAdult = wrapper.vm.getFormattedGuest(notSelectedAdult);
		expect([null, undefined]).toContain(formattedNotSelectedAdult.face);

		const notSelectedChild = { pax_type: "CH" };
		const formattedNotSelectedChild = wrapper.vm.getFormattedGuest(notSelectedChild);
		expect([null, undefined]).toContain(formattedNotSelectedChild.face);

		const notSelectedBaby = { pax_type: "BB" };
		const formattedNotSelectedBaby = wrapper.vm.getFormattedGuest(notSelectedBaby);
		expect([null, undefined]).toContain(formattedNotSelectedBaby.face);
	});

	it("Sets default values for guest checkboxes when not configured", () => {
		const guest = {};
		const wrapper = shallowMount(Confirmation, { store, i18n, router });
		
		const formattedGuest = wrapper.vm.getFormattedGuest(guest);
		
		expect(formattedGuest.data_protect).toBe(true);
		expect(formattedGuest.terms_conditions).toBe(true);
		expect(formattedGuest.commercial_data).toBe(true);
		expect(formattedGuest.commercial_data_share).toBeNull();
	});

	it("Overrides default checkbox states with configured values", () => {
		const guest = {
			checkboxStates: {
				data_protect: false,
				terms_conditions: false,
				commercial_data: true,
				commercial_data_share: true,
			},
		};
		const wrapper = shallowMount(Confirmation, { store, i18n, router });
		
		const formattedGuest = wrapper.vm.getFormattedGuest(guest);
	
		expect(formattedGuest.data_protect).toBe(false);
		expect(formattedGuest.terms_conditions).toBe(false);
		expect(formattedGuest.commercial_data).toBe(true);
		expect(formattedGuest.commercial_data_share).toBe(true);
	});
	
});

describe("Redirect Link logic", () => {
	let store;
	beforeEach(() => {
		const AutoCheckinApi = repository.get("checkin");
		store = new Vuex.Store({
			modules: {
				queryParams: queryParamsModule,
				brand: {
					...brandModule,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							...defaultConfig,
							redirect_link: true,
							custom_confirmation_text: true,
							partial_checkin: true,
						},
						products: [
							{
								id: 15,
								product_id: 22,
								active: 1,
								name: "portal_redirect",
								active_by_name: { portal_redirect: 1 },
							},
						],
					},
				},
				app: {
					...app,
					state: {
						...app.state,
						validateDataCheck: true,
					},
				},
				scan: scanModule,
				reservations: {
					...reservationModule,
					state: {
						reservationSelected: finishedReservation.data[1],
						reservationCheckinComplete: true,
					},
				},
				guest: guestModule,
				loading,
			},
		});

		api.sendGuestToPMS = jest.fn();
		AutoCheckinApi.getCustomizedText = jest.fn(() =>
			Promise.resolve({
				data: [
					{
						translations: [
							{
								text: "test",
							},
						],
					},
				],
			}),
		);
	});

	it("If brand config has redirect_link and portal_redirect product, getRedirectLink endpoint should be called and component should be rendered", async () => {
		api.getRedirectUrl = jest.fn(() => Promise.resolve(redirectUrlResponse));

		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});
		wrapper.vm.checkGuestsHaveCompletedCheckin = jest.fn(() => true);

		expect(wrapper.vm.redirectLinkAvailable).toBe(false);
		expect(wrapper.vm.redirectLink).toEqual("");
		expect(wrapper.find('[data-test="redirect-link"]').exists()).toBe(false);

		expect(await api.getRedirectUrl).toBeCalled();
		await flushPromises;

		expect(wrapper.vm.redirectLinkAvailable).toBe(true);
		expect(wrapper.vm.redirectLink).toEqual("https://hotelinking.com");
		expect(wrapper.find('[data-test="redirect-link"]').exists()).toBe(true);
	});

	it("If brand config has no redirect_link nor portal_redirect product, getRedirectLink endpoint should not be called", async () => {
		api.getRedirectUrl = jest.fn(() => Promise.resolve(redirectUrlResponse));

		store = new Vuex.Store({
			modules: {
				queryParams: queryParamsModule,
				brand: {
					...brandModule,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							...defaultConfig,
							redirect_link: false,
							custom_confirmation_text: true,
							partial_checkin: true,
						},
						products: [
							{
								id: 15,
								product_id: 22,
								active: 1,
								name: "portal_redirect",
								active_by_name: { portal_redirect: 1 },
							},
						],
					},
				},
				scan: scanModule,
				reservations: {
					...reservationModule,
					state: {
						reservationSelected: finishedReservation.data[1],
						reservationCheckinComplete: true,
					},
				},
				guest: guestModule,
				loading,
			},
		});

		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});
		wrapper.vm.checkGuestsHaveCompletedCheckin = jest.fn(() => true);

		expect(await api.getRedirectUrl).not.toBeCalled();
		await flushPromises;

		expect(wrapper.vm.redirectLinkAvailable).toBe(false);
		expect(wrapper.vm.redirectLink).toEqual("");
		expect(wrapper.find('[data-test="redirect-link"]').exists()).toBe(false);
	});

	it("getRedirectLink endpoint fails, expect a log and keep going and dont print redirectLink component", async () => {
		api.getRedirectUrl = jest.fn(() =>
			Promise.reject({
				error: ["getRedirectUrlError"],
			}),
		);

		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});
		wrapper.vm.checkGuestsHaveCompletedCheckin = jest.fn(() => true);

		expect(await api.getRedirectUrl).toBeCalled();

		await flushPromises;

		expect(wrapper.vm.redirectLinkAvailable).toBe(false);
		expect(wrapper.vm.redirectLink).toEqual("");
		expect(wrapper.find('[data-test="redirect-link"]').exists()).toBe(false);
	});
});

describe("Redirect logic if error is returned", () => {
	let store;
	let redirect;
	beforeEach(() => {
		redirect = jest.spyOn(redirectMixin.methods, "redirect");
		const api = repository.get("integration");
		store = new Vuex.Store({
			modules: {
				queryParams: queryParamsModule,
				brand: {
					...brandModule,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							...defaultConfig.data,
						},
					},
				},
				scan: scanModule,
				reservations: {
					...reservationModule,
					state: {
						reservationSelected: finishedReservation.data[1],
					},
				},
				app: {
					...app,
					state: {
						...app.state,
						validateDataCheck: true,
					},
				},
				guest: {
					...guestModule,
					getters: {
						getSelectedGuest: () => {
							return guestData[0];
						},
						getGuestsToSynchronize: () => {
							return [guestData[0]];
						},
					},
				},
				loading,
			},
		});

		api.sendGuestToPMS = jest.fn(async () =>
			Promise.reject({
				response: {
					data: {
						error: {
							code: "INT_3_2",
						},
					},
				},
			}),
		);
	});

	it("If reservation returns a Timeout from pms, redirect to error page", async () => {
		api.sendGuestToPMS = jest.fn(async () =>
			Promise.resolve({
				data: [],
				error: {
					code: "INT_2_2", // Timeout fron pms
				},
			}),
		);
		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});

		await flushPromises();
		expect(await api.sendGuestToPMS).toHaveBeenCalled();
		// await new Promise(process.nextTick);
		expect(redirect).toBeCalledWith({ name: "Error" });
		wrapper.destroy();
	});
});

describe("behaviour when get reservation with 200 but with error but no data object", () => {
	let store;
	beforeEach(() => {
		store = new Vuex.Store({
			modules: {
				queryParams: queryParamsModule,
				brand: {
					...brandModule,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							...defaultConfig.data,
						},
					},
				},
				// scan: scanModule,
				reservations: {
					...reservationModule,
					state: {
						reservationSelected: undefined,
						error: reservationLogicError.error,
					},
				},
				app: {
					...app,
					state: {
						...app.state,
						validateDataCheck: true,
					},
				},
				guest: {
					...guestModule,
					getters: {
						getSelectedGuest: () => {
							return guestData[0];
						},
					},
				},
				loading,
			},
		});
	});
	it("shouldn't render continue buttons if there's no reservation existence", async () => {
		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});
		const buttonSection = wrapper.find('[data-test="btn-section"]');
		await nextTick();
		expect(wrapper.vm.$data.errorExist).toBe(true);
		expect(buttonSection.isVisible()).toBe(false);
	});
	it("shouldn't render QR if there's an error existing", async () => {
		store.state.brand.config.show_qr_code = true;
		const wrapper = shallowMount(Confirmation, {
			store,
			i18n,
			router,
		});
		const qrSection = wrapper.find('[data-test="qr-code"]');
		expect(qrSection.isVisible()).toBe(false);
		await nextTick();
	});
});
