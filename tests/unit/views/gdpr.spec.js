import Vuex from "vuex";
import VueRouter from "vue-router";
import { shallowMount, createLocalVue } from "@vue/test-utils";
import flushPromises from "flush-promises";
import { i18n } from "@/locales";
import Gdpr from "@/views/Gdpr.vue";
import gdpr from "@/store/gdpr.js";
import app from "@/store/app.js";
import guest from "@/store/guest.js";
import brand from "@/store/brand.js";
import redirectMixin from "@/mixins/redirect";
import {
  defaultConfig,
  defaultBrandGdpr,
} from "../../../mocks/modules/brand/data";

import repository from "@/repository/repositoryFactory.js";

const api = repository.get("brand");

const localVue = createLocalVue();
localVue.use(Vuex);
localVue.use(VueRouter);

describe("Gdpr page", () => {
  let store;
  let actions;
  let wrapper;
  let redirect;
  let mockRouter;

  beforeEach(() => {
    redirect = jest.spyOn(redirectMixin.methods, "redirect");
    mockRouter = { push: jest.fn(() => Promise.resolve()) };
    actions = {
      "loading/LOADING": jest.fn(),
      "brand/SET_BRAND": jest.fn(),
      "brand/SET_CONFIG": jest.fn(),
      "gdpr/SET_GDPR_TEXT": jest.fn(),
      "gdpr/SET_GDPR_MODAL": jest.fn(),
    };

    store = new Vuex.Store({
      actions,
      modules: {
        brand: {
          ...brand,
          state: {
            brandId: 1,
            config: {
              ...defaultConfig,
              custom_gdpr_text: false,
            },
          },
        },
        gdpr,
      },
    });
  });

  it("GDPR page is mounted", async () => {
    api.getBrandGDPR = jest.fn(async () => Promise.resolve(defaultBrandGdpr));
    wrapper = shallowMount(Gdpr, {
      store,
      i18n,
    });
    expect(wrapper.is(Gdpr)).toBe(true);
  });

  it("Api methods are called", async () => {
    expect(api.getBrandGDPR).toHaveBeenCalledTimes(1);
  });

  it("gdpr store has api texts", async () => {
    expect(store.state.gdpr).toStrictEqual({
      text: defaultBrandGdpr.data.second_eprivacy_page,
      modal: defaultBrandGdpr.data.legal_text,
      accepted: false,
      lang: "en",
    });
  });

  it("text is present on component", async () => {
    expect(wrapper.find("p").html()).toContain(
      defaultBrandGdpr.data.second_eprivacy_page
    );
  });

  it("If we have all data, fetch is not needed", async () => {
    jest.clearAllMocks();
    store = new Vuex.Store({
      actions,
      modules: {
        brand: {
          state: {
            brandId: 1,
            config: {
              test: "test",
            },
          },
          namespaced: true,
        },
        gdpr: {
          state: {
            text: "<div>test</div>",
            modal: "test",
          },
          namespaced: true,
        },
      },
    });

    wrapper = shallowMount(Gdpr, {
      store,
      i18n,
    });

    expect(wrapper.find("p").html()).toContain("test");
  });

  it("If language is not the same, fetch again", async () => {
    jest.clearAllMocks();
    store = new Vuex.Store({
      actions,
      modules: {
        brand: {
          state: {
            brandId: 1,
            config: {
              test: "test",
            },
          },
          namespaced: true,
        },
        gdpr: {
          state: {
            text: "<div>test</div>",
            modal: "test",
          },
          namespaced: true,
        },
      },
    });

    wrapper = shallowMount(Gdpr, {
      store,
      i18n,
    });

    expect(api.getBrandGDPR).toHaveBeenCalledTimes(1);
  });

  it("If no config is present, redirect to Error page", async () => {
    // We need to clear mocks in order to test other logic
    jest.clearAllMocks();

    store = new Vuex.Store({
      actions,
      modules: {
        brand: {
          ...brand,
          state: {
            brandId: 1,
            config: {},
          },
        },
        gdpr,
      },
    });

    wrapper = shallowMount(Gdpr, {
      mocks: {
        $router: mockRouter,
      },
      store,
      i18n,
    });

    expect(redirect).toBeCalledWith({ name: "Error" });
  });
});

describe("Custom GDPR text logic", () => {
  let store;
  let actions;
  let wrapper;

  beforeEach(() => {
    const AutoCheckinApi = repository.get("checkin");

    actions = {
      "loading/LOADING": jest.fn(),
      "brand/SET_BRAND": jest.fn(),
      "brand/SET_CONFIG": jest.fn(),
      "gdpr/SET_GDPR_TEXT": jest.fn(),
      "gdpr/SET_GDPR_MODAL": jest.fn(),
    };

    store = new Vuex.Store({
      actions,
      modules: {
        brand: {
          ...brand,
          state: {
            brandId: 1,
            config: {
              ...defaultConfig,
              custom_gdpr_text: true,
            },
          },
        },
        gdpr,
      },
    });

    AutoCheckinApi.getCustomizedText = jest.fn(() =>
      Promise.resolve({
        data: [
          {
            translations: [
              {
                text: "Testing Custom GDPR text",
              },
            ],
          },
        ],
      })
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
    wrapper.destroy();
  });

  it("If config has custom_gdpr_text, getCustomGDPRText method is called", async () => {
    wrapper = shallowMount(Gdpr, {
      store,
      i18n,
    });

    jest.spyOn(wrapper.vm, "getCustomGDPRText");
    await flushPromises();
    expect(await wrapper.vm.getCustomGDPRText).toBeCalled();
  });

  it("If config does not have custom_gdpr_text, default text will be shown", async () => {
    api.getBrandGDPR = jest.fn(async () => Promise.resolve(defaultBrandGdpr));

    store = new Vuex.Store({
      actions,
      modules: {
        brand: {
          ...brand,
          state: {
            brandId: 1,
            config: {
              ...defaultConfig,
              custom_gdpr_text: false,
            },
          },
        },
        gdpr: {
          state: {
            text: defaultBrandGdpr.data.second_eprivacy_page,
            modal: defaultBrandGdpr.data.legal_text,
            lang: "en",
          },
          namespaced: true,
        },
      },
    });

    wrapper = shallowMount(Gdpr, {
      store,
      i18n,
    });

    jest.spyOn(wrapper.vm, "getCustomGDPRText");

    await flushPromises();
    expect(await wrapper.vm.getCustomGDPRText).not.toBeCalled();
    expect(wrapper.find("p").html()).toContain(
      defaultBrandGdpr.data.second_eprivacy_page
    );
  });
});

describe("Reception mode", () => {
  let store;
  let actions;
  let wrapper;
  let redirect;
  let mockRouter;

  beforeEach(() => {
    redirect = jest.spyOn(redirectMixin.methods, "redirect");
    mockRouter = { push: jest.fn(() => Promise.resolve()) };
    actions = {
      "loading/LOADING": jest.fn(),
      "brand/SET_BRAND": jest.fn(),
      "brand/SET_CONFIG": jest.fn(),
      "gdpr/SET_GDPR_TEXT": jest.fn(),
      "gdpr/SET_GDPR_MODAL": jest.fn(),
      "gdpr/SET_GDPR_ACCEPTED": jest.fn(),
    };

    store = new Vuex.Store({
      actions,
      modules: {
        brand: {
          ...brand,
          state: {
            brandId: 1,
            config: {
              ...defaultConfig.data,
              reception_signature: true,
            },
          },
        },
        guest: {
          ...guest,
          getters: {
            getSelectedGuest: () => {
              return { pax_type: "AD" };
            },
          },
        },
        gdpr,
        app,
      },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("redirects to ValidateData if guest has date_of_issue", async () => {
    const acceptPrivacy = jest.spyOn(Gdpr.methods, "acceptPrivacy");

    wrapper = shallowMount(Gdpr, {
      mocks: {
        $router: mockRouter,
      },
      store,
      i18n,
    });

    wrapper.vm.$store.state.app.isReceptionMode = true;

    await wrapper.setData({
      guestData: {
        date_of_issue: "2023-12-24",
        pax_type: "AD",
      },
    });

    await wrapper.find('[data-test="acceptConditionsButton"]').trigger("click");
    expect(acceptPrivacy).toBeCalled();
    await flushPromises();

    expect(redirect).toBeCalledWith({ name: "ValidateData" });
  });

  it("redirects to Scan if guest has no date_of_issue", async () => {
    const acceptPrivacy = jest.spyOn(Gdpr.methods, "acceptPrivacy");

    wrapper = shallowMount(Gdpr, {
      mocks: {
        $router: mockRouter,
      },
      store,
      i18n,
    });

    wrapper.vm.$store.state.app.isReceptionMode = true;

    await wrapper.setData({
      guestData: {
        date_of_issue: null,
        pax_type: "AD",
      },
    });

    await wrapper.find('[data-test="acceptConditionsButton"]').trigger("click");
    expect(acceptPrivacy).toBeCalled();
    await flushPromises();

    expect(redirect).toBeCalledWith({ name: "Scan" });
  });
});
