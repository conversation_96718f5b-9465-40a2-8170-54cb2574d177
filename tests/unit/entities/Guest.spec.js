import Guest from "@/entities/Guest";

describe("Guest Entity", () => {
  describe("documentsEmail field", () => {
    it("should initialize documentsEmail as null when not provided", () => {
      const guestData = {
        name: "<PERSON>",
        surname: "<PERSON><PERSON>",
        email: "<EMAIL>"
      };

      const guest = new Guest(guestData);

      expect(guest.email).toBe("<EMAIL>");
      expect(guest.documentsEmail).toBeNull();
    });

    it("should initialize documentsEmail when provided", () => {
      const guestData = {
        name: "<PERSON>",
        surname: "<PERSON><PERSON>",
        email: "<EMAIL>",
        documentsEmail: "<EMAIL>"
      };

      const guest = new Guest(guestData);

      expect(guest.email).toBe("<EMAIL>");
      expect(guest.documentsEmail).toBe("<EMAIL>");
    });

    it("should keep email and documentsEmail separate", () => {
      const guestData = {
        name: "<PERSON>",
        surname: "<PERSON><PERSON>",
        email: "<EMAIL>",
        documentsEmail: "<EMAIL>"
      };

      const guest = new Guest(guestData);

      expect(guest.email).toBe("<EMAIL>");
      expect(guest.documentsEmail).toBe("<EMAIL>");
      expect(guest.email).not.toBe(guest.documentsEmail);
    });

    it("should handle empty documentsEmail", () => {
      const guestData = {
        name: "John",
        surname: "Doe",
        email: "<EMAIL>",
        documentsEmail: ""
      };

      const guest = new Guest(guestData);

      expect(guest.email).toBe("<EMAIL>");
      expect(guest.documentsEmail).toBeNull();
    });
  });
});
