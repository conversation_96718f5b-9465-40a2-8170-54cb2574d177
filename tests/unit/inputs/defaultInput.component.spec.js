import Vuex from "vuex";
import { mount, createLocalVue } from "@vue/test-utils";
import defaultInput from "@/components/search/inputs/defaultInput.component.vue";
import Vuelidate from "vuelidate";
import { i18n } from "@/locales";
import { validateIEorUKPostalCode, shouldValidateIEorUK, validatePostalCodeForCountry } from "@/utils/address";
import * as countriesModule from "@/utils/countries";

// Mock the address.js functions
jest.mock("@/utils/address", () => ({
  validateIEorUKPostalCode: jest.fn(),
  shouldValidateIEorUK: jest.fn(),
  validatePostalCodeForCountry: jest.fn()
}));

// Mock the countryList
jest.mock("@/utils/countries", () => ({
  countryList: [
    { code: "ESP", iso2Code: "ES" },
    { code: "IRL", iso2Code: "IE" },
    { code: "GBR", iso2Code: "GB" }
  ]
}));

const localVue = createLocalVue();
localVue.use(Vuelidate);
localVue.use(Vuex);

describe("Default input", () => {
	it("should render", () => {
		const wrapper = mount(defaultInput, {
			localVue,
			propsData: {
				name: "test",
				inputName: "test",
				type: "defaultInput",
				value: "",
				optional: false,
				autocompleteData: {
					name: "test",
					autocomplete: "test",
				},
			},
		});
		expect(wrapper.find('[data-test="test"]').exists()).toBe(true);
	});

	it("If input has an error, border should be red and error message should be shown", async () => {
		const wrapper = mount(defaultInput, {
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "defaultInput",
				value: "testing",
				optional: false,
				autocompleteData: {
					name: "test",
					autocomplete: "test",
				},
			},
		});

		const input = wrapper.find('[data-test="test"]');
		expect(input.exists()).toBe(true);

		input.setValue("");
		await wrapper.vm.$nextTick();
		expect(wrapper.find(".border-red-400").exists()).toBe(true);
		expect(wrapper.find(".error-message").text()).toBe(
			"The value cannot be empty",
		);
	});

	it("If input does not have an error, border should be green and no error message should be shown", async () => {
		const wrapper = mount(defaultInput, {
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "defaultInput",
				value: "",
				optional: false,
				autocompleteData: {
					name: "test",
					autocomplete: "test",
				},
			},
		});

		const input = wrapper.find('[data-test="test"]');
		expect(input.exists()).toBe(true);

		input.setValue("testing!!!");
		await wrapper.vm.$nextTick();
		expect(wrapper.find(".border-green-400").exists()).toBe(true);
		expect(wrapper.find(".error-message").exists()).toBe(false);
	});

	it("If input is optional, and value is cleared, gray border should be shown and no error message", async () => {
		const wrapper = mount(defaultInput, {
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "defaultInput",
				value: "test",
				optional: true,
				autocompleteData: {
					name: "test",
					autocomplete: "test",
				},
			},
		});

		const input = wrapper.find('[data-test="test"]');
		expect(input.exists()).toBe(true);

		input.setValue("");
		await wrapper.vm.$nextTick();
		expect(wrapper.find(".border-gray-200").exists()).toBe(true);
		expect(wrapper.find(".border-red-400").exists()).toBe(false);
		expect(wrapper.find(".border-green-400").exists()).toBe(false);
		expect(wrapper.find(".error-message").exists()).toBe(false);
	});

	it("If input max length is exceeded, error should be shown", async () => {
		const wrapper = mount(defaultInput, {
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "defaultInput",
				value: "",
				maxLength: "10",
				optional: false,
				autocompleteData: {
					name: "test",
					autocomplete: "test",
				},
			},
		});

		const input = wrapper.find('[data-test="test"]');
		expect(input.exists()).toBe(true);

		input.setValue("1234567898745");
		await wrapper.vm.$nextTick();
		expect(wrapper.find(".border-green-400").exists()).toBe(false);
		expect(wrapper.find(".border-red-400").exists()).toBe(true);
		expect(wrapper.find(".error-message").text()).toBe(
			"Value must contain a maximum of 10 characters",
		);
	});

	it("If information modal button is clicked, a modal should pop up", async () => {
		const store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						mainColor: "123",
					},
				},
			},
		});

		const wrapper = mount(defaultInput, {
			store,
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "defaultInput",
				value: "",
				state: "test",
				maxLength: "10",
				optional: false,
				infoModal: {
					title: "Obtener información de la reserva.",
					message:
						"Podrá encontrar la información necesaria para realizar el check-in en los datos suministrados por el establecimiento, agencia o Web de reservas en el momento de hacer la reserva. El código o                      localizador de reserva es generalmente un código alfanumérico proporcionado en el momento de haber hecho la reserva.",
					button: "Volver",
				},
				autocompleteData: {
					name: "test",
					autocomplete: "test",
				},
			},
		});

		const button = wrapper.find('[data-test="info-button-help"]');
		expect(button.exists()).toBe(true);
		await button.trigger("click");
		expect(wrapper.emitted("showModalInfo")).toBeTruthy();
	});

	it("If input min length is not reached, error should be shown", async () => {
		const wrapper = mount(defaultInput, {
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "defaultInput",
				value: "",
				minLength: "8",
				optional: false,
				autocompleteData: {
					name: "test",
					autocomplete: "test",
				},
			},
		});

		const input = wrapper.find('[data-test="test"]');
		expect(input.exists()).toBe(true);

		input.setValue("123456");
		await wrapper.vm.$nextTick();
		expect(wrapper.find(".border-green-400").exists()).toBe(false);
		expect(wrapper.find(".border-red-400").exists()).toBe(true);
		expect(wrapper.find(".error-message").text()).toBe(
			"Value must contain a minimum of 8 characters",
		);
	});
});

// Add new test suite for postalCodeCheck method
describe("postalCodeCheck method", () => {
  // Setup for each test
  let postalCodeCheck;
  let component;
  let findSpy;

  beforeEach(() => {
    // Reset mocks
    validateIEorUKPostalCode.mockReset();
    shouldValidateIEorUK.mockReset();
    validatePostalCodeForCountry.mockReset();
    
    // Create a spy on the find method of countryList
    findSpy = jest.spyOn(countriesModule.countryList, 'find');
    
    // Create a Vue component constructor
    const ComponentClass = localVue.extend(defaultInput);
    
    // Create an instance of the component
    component = new ComponentClass({
      propsData: {
        name: "test",
        inputName: "postal_code",
        type: "text",
        value: "",
        optional: true,
        autocompleteData: {
          name: "test",
          autocomplete: "test"
        }
      }
    });
    
    // Initialize the component
    component.$mount();
    
    // Get the method from the component
    postalCodeCheck = component.postalCodeCheck;
  });

  afterEach(() => {
    // Restore the original implementation
    findSpy.mockRestore();
  });

  it("returns true for empty values", () => {
    expect(postalCodeCheck("")).toBe(true);
    expect(postalCodeCheck(null)).toBe(true);
    expect(postalCodeCheck(undefined)).toBe(true);
    expect(findSpy).not.toHaveBeenCalled();
  });

  it("trims input values", () => {
    component.countryCode = "ESP";
    shouldValidateIEorUK.mockReturnValue(false);
    validatePostalCodeForCountry.mockReturnValue(true);
    
    postalCodeCheck("  28001  ");
    
    expect(findSpy).toHaveBeenCalledWith(expect.any(Function));
    expect(validatePostalCodeForCountry).toHaveBeenCalledWith("28001", "ES");
  });

  it("uses validateIEorUKPostalCode for Ireland", () => {
    component.countryCode = "IRL";
    shouldValidateIEorUK.mockReturnValue(true);
    validateIEorUKPostalCode.mockReturnValue(true);
    
    const result = postalCodeCheck("D01F9C9");
    
    expect(findSpy).toHaveBeenCalledWith(expect.any(Function));
    expect(shouldValidateIEorUK).toHaveBeenCalledWith("IE");
    expect(validateIEorUKPostalCode).toHaveBeenCalledWith("D01F9C9");
    expect(validatePostalCodeForCountry).not.toHaveBeenCalled();
    expect(result).toBe(true);
  });

  it("uses validatePostalCodeForCountry for non-Ireland countries", () => {
    component.countryCode = "ESP";
    shouldValidateIEorUK.mockReturnValue(false);
    validatePostalCodeForCountry.mockReturnValue(true);
    
    const result = postalCodeCheck("28001");
    
    expect(findSpy).toHaveBeenCalledWith(expect.any(Function));
    expect(shouldValidateIEorUK).toHaveBeenCalledWith("ES");
    expect(validateIEorUKPostalCode).not.toHaveBeenCalled();
    expect(validatePostalCodeForCountry).toHaveBeenCalledWith("28001", "ES");
    expect(result).toBe(true);
  });

  it("returns false for invalid postal codes", () => {
    component.countryCode = "ESP";
    shouldValidateIEorUK.mockReturnValue(false);
    validatePostalCodeForCountry.mockReturnValue(false);
    
    const result = postalCodeCheck("invalid");
    
    expect(findSpy).toHaveBeenCalledWith(expect.any(Function));
    expect(result).toBe(false);
  });

  it("handles country not found in countryList", () => {
    component.countryCode = "XXX"; // Non-existent country code
    
    // Mock the find method to return undefined for this test
    findSpy.mockReturnValueOnce(undefined);
    
    shouldValidateIEorUK.mockReturnValue(false);
    validatePostalCodeForCountry.mockReturnValue(true);
    
    const result = postalCodeCheck("12345");
    
    expect(findSpy).toHaveBeenCalledWith(expect.any(Function));
    expect(shouldValidateIEorUK).toHaveBeenCalledWith(undefined);
    expect(validatePostalCodeForCountry).toHaveBeenCalledWith("12345", undefined);
    expect(result).toBe(true);
  });
});
