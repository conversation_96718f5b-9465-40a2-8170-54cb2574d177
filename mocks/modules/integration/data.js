import { format } from "date-fns";

export const childReservation = {
	data: [
		{
			brand_id: "1",
			guests: [
				{
					pms_id: "HUES-MOCK",
					pax_type: null,
					first_name: "<PERSON>ck_<PERSON>",
					last_name: "<PERSON><PERSON>_Lastname",
					email: null,
					gender: "male",
					birthday: "1986-07-17",
					nationality: "ES",
					document_id: "MOCK_NIE",
					address: "Mock_Address",
					city: "Mock_City",
					province: null,
					postal_code: "",
					telephone: "",
					birth_country: "ES",
					residence_country: "Espa\u00f1a",
				},
			],
			check_in: "2019-02-14",
			check_out: "2019-02-15",
			res_date: "2019-02-14",
			res_room_number: "0314",
			res_room_type: "DOBLE USO",
			res_board: "AL",
			booking_state: "closed",
			stay_state: "closed",
			check_state: "none",
			check_state_date: "1900-01-01",
			res_adults: "1",
			res_children: null,
			res_juniors: null,
			res_babies: null,
			res_seniors: null,
			res_id: "123456",
			res_nights: "1",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "",
			res_contract: null,
			res_comments: "",
			res_amount: "43.5",
			res_extras: "[]",
			res_currency: "EUR",
		},
	],
};

export const defaultReservation = {
	data: [
		{
			brand_id: "1",
			guests: [
				{
					pms_id: "HUES-744381",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: "Mokhles",
					last_name: "Pahlan",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: "15CV28143",
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
				{
					pms_id: "HUES-744382",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: "Test",
					last_name: "Testing",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: "58749865E",
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
				{
					pms_id: null,
					position: null,
					validated: false,
					pax_type: "CH",
					first_name: null,
					last_name: null,
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: null,
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
			],
			check_in: "2022-02-14",
			check_out: "2022-02-15",
			res_date: null,
			res_room_number: "",
			res_room_type: "DH",
			res_board: "AL",
			booking_state: "reserved",
			stay_state: "scheduled",
			check_state: "none",
			check_state_date: "1900-01-01",
			res_adults: "2",
			res_children: "1",
			res_juniors: null,
			res_babies: null,
			res_seniors: null,
			res_localizer: "TESTLOCALIZER7",
			res_id: "**********",
			res_nights: "1",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "EXPEDIA.COM",
			res_contract: null,
			res_comments: "",
			res_amount: 0,
			res_extras: "[]",
			res_currency: "EUR",
		},
	],
};
export const completedReservation = {
	data: [
		{
			brand_id: 76,
			guests: [
				{
					pms_id: "HUES-744381|1",
					position: "1",
					validated: true,
					holder: true,
					pax_type: "AD",
					first_name: "Marina",
					last_name: "Lopez",
					surname: "Lopez",
					second_surname: "Lopez",
					email: "<EMAIL>",
					gender: "female",
					birthday: "1998-02-05",
					nationality: "ESP",
					document_id: "99999999R",
					document_type: "identity_card",
					document_date_issue: "2015-12-25T00:00:00",
					document_date_expiry: "2030-12-24T00:00:00",
					document_support_number: "CRS855432",
					address: "C. Miguel Forteza I Piña, 3",
					city: "Palma",
					province: "Baleares",
					postal_code: "07004",
					telephone: *********,
					birth_country: "ESP",
					residence_country: "ESP",
					lang: "es",
					kinship: null
				},
				{
					pms_id: "HUES-744381|2",
					position: "2",
					validated: true,
					holder: true,
					pax_type: "AD",
					first_name: "Adrián",
					last_name: "Martorell",
					surname: "Martorell",
					second_surname: "Martorell",
					email: null,
					gender: "male",
					birthday: "1998-02-05",
					nationality: "ESP",
					document_id: "99999999R",
					document_type: "identity_card",
					document_date_issue: null,
					document_date_expiry: "2030-12-24T00:00:00",
					document_support_number: "CRS855432",
					address: null,
					city: "Palma",
					province: "Baleares",
					postal_code: "07004",
					telephone: null,
					birth_country: "ESP",
					residence_country: "ESP",
					lang: "es",
					kinship: null
				},
				{
					pms_id: null,
					position: "3",
					validated: true,
					holder: false,
					pax_type: "CH",
					first_name: "Ana",
					last_name: "Martorell",
					surname: null,
					second_surname: null,
					email: null,
					gender: "female",
					birthday: null,
					nationality: null,
					document_id: null,
					document_type: null,
					document_date_issue: null,
					document_date_expiry: null,
					document_support_number: null,
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
					lang: null,
					kinship: null
				}
			],
			check_in: "2022-02-14",
			check_out: "2022-02-15",
			res_date: null,
			res_room_number: "0504",
			res_room_type: "DH",
			res_board: "AL",
			res_adults: "2",
			res_juniors: null,
			res_children: "1",
			res_babies: null,
			res_localizer: "TESTLOCALIZER7",
			res_id: "**********",
			res_nights: "1",
			res_channel: "EXPEDIA.COM",
			res_data: "{\"roominglist_obj_id\":\"HUES-744381\",\"res_type\":\"1\",\"no_of_rooms\":\"1\",\"no_of_pax\":\"3\"}",
			booking_state: "reserved",
			check_state: "none"
		}
	]
};


export const receptionReservation = {
	data: [
		{
			brand_id: "76",
			guests: [
				{
					address: {
						street_name: "C. Miguel Forteza I Piña",
						house_number: "3",
						postcode: "07007",
						province: "Illes Balears",
					},
					birth_country: "BOL",
					birthday: "1998-02-05",
					city: "Palma",
					document_id: "56746654L",
					document_type: "identity_card",
					date_of_issue: "2015-12-25T00:00:00",
					date_of_expiry: "2025-12-24T00:00:00",
					email: "<EMAIL>",
					first_name: "Alex",
					last_name: "Lopez",
					surname: "Quiroga",
					second_surname: "Marcelo",
					gender: "male",
					holder: true,
					lang: "es",
					pax_type: "AD",
					pms_id: "HUES-744381",
					position: null,
					residence_country: "ES",
					telephone: {
						countryCode: "ES",
						dialCode: "+34",
						value: "666000999",
					},
					validated: false,
					nationality: "ESP",
					province: "Baleares",
					postal_code: "07004",
				},
				{
					pms_id: "HUES-744382",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: "Test",
					last_name: "Testing",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: "58749865E",
					document_type: "identity_card",
					date_of_issue: "2015-12-25T00:00:00",
					date_of_expiry: "2025-12-24T00:00:00",
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
				{
					pms_id: null,
					position: null,
					validated: false,
					pax_type: "CH",
					first_name: null,
					last_name: null,
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: null,
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
			],
			check_in: "2022-02-14",
			check_out: "2022-02-15",
			res_date: null,
			res_room_number: "0504",
			res_room_type: "DH",
			res_board: "AL",
			booking_state: "reserved",
			stay_state: "scheduled",
			check_state: "none",
			check_state_date: "1900-01-01",
			res_adults: "2",
			res_children: "1",
			res_juniors: null,
			res_babies: null,
			res_seniors: null,
			res_localizer: "TESTLOCALIZER7",
			res_id: "**********",
			res_nights: "1",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "EXPEDIA.COM",
			res_contract: null,
			res_comments: "",
			res_amount: 0,
			res_extras: "[]",
			res_currency: "EUR",
		},
	],
};
export const receptionReservationWithNullDateOfIssue = {
  data: [
    {
      brand_id: "76",
      guests: [
        {
          address: {
            street_name: "C. Miguel Forteza I Piña",
            house_number: "3",
            postcode: "07007",
            province: "Illes Balears",
          },
          birth_country: "BOL",
          birthday: "1998-02-05",
          city: "Palma",
          document_id: "56746654L",
          document_type: "identity_card",
          date_of_issue: null,
          date_of_expiry: "2025-12-24T00:00:00",
          email: "<EMAIL>",
          first_name: "Alex",
          last_name: "Lopez",
          surname: "Quiroga",
          second_surname: "Marcelo",
          gender: "male",
          holder: true,
          lang: "es",
          pax_type: "AD",
          pms_id: "HUES-744381",
          position: null,
          residence_country: "ES",
          telephone: {
            countryCode: "ES",
            dialCode: "+34",
            value: "666000999",
          },
          validated: false,
          nationality: "ESP",
          province: "Baleares",
          postal_code: "07004",
        },
        {
          pms_id: "HUES-744382",
          position: null,
          validated: false,
          pax_type: "AD",
          first_name: "Test",
          last_name: "Testing",
          email: null,
          gender: null,
          birthday: null,
          nationality: null,
          document_id: "58749865E",
          document_type: "identity_card",
          date_of_issue: null,
          date_of_expiry: "2025-12-24T00:00:00",
          address: null,
          city: null,
          province: null,
          postal_code: null,
          telephone: null,
          birth_country: null,
          residence_country: null,
        },
        {
          pms_id: null,
          position: null,
          validated: false,
          pax_type: "CH",
          first_name: null,
          last_name: null,
          email: null,
          gender: null,
          birthday: null,
          nationality: null,
          document_id: null,
          address: null,
          city: null,
          province: null,
          postal_code: null,
          telephone: null,
          birth_country: null,
          residence_country: null,
        },
      ],
      check_in: "2022-02-14",
      check_out: "2022-02-15",
      res_date: null,
      res_room_number: "0504",
      res_room_type: "DH",
      res_board: "AL",
      booking_state: "reserved",
      stay_state: "scheduled",
      check_state: "none",
      check_state_date: "1900-01-01",
      res_adults: "2",
      res_children: "1",
      res_juniors: null,
      res_babies: null,
      res_seniors: null,
      res_localizer: "TESTLOCALIZER7",
      res_id: "**********",
      res_nights: "1",
      res_agency: null,
      res_company: null,
      res_intermediary: null,
      res_channel: "EXPEDIA.COM",
      res_contract: null,
      res_comments: "",
      res_amount: 0,
      res_extras: "[]",
      res_currency: "EUR",
    },
  ],
};


export const langReservation = {
	data: [
		{
			brand_id: "1",
			guests: [
				{
					pms_id: "HUES-744381",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: "Mokhles",
					last_name: "Pahlan",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: "15CV28143",
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					lang: "pt",
					birth_country: null,
					residence_country: null,
				},
				{
					pms_id: "HUES-744382",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: "Test",
					last_name: "Testing",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: "58749865E",
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					lang: "de",
					birth_country: null,
					residence_country: null,
				},
			],
			check_in: "2022-02-14",
			check_out: "2022-02-15",
			res_date: null,
			res_room_number: "",
			res_room_type: "DH",
			res_board: "AL",
			booking_state: "reserved",
			stay_state: "scheduled",
			check_state: "none",
			check_state_date: "1900-01-01",
			res_adults: "1",
			res_children: "1",
			res_juniors: null,
			res_babies: null,
			res_seniors: null,
			res_localizer: "TESTLOCALIZER7",
			res_id: "**********",
			res_nights: "1",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "EXPEDIA.COM",
			res_contract: null,
			res_comments: "",
			res_amount: 0,
			res_extras: "[]",
			res_currency: "EUR",
		},
	],
};

export const multipleRoomsReservation = {
	data: [
		{
			brand_id: "1",
			guests: [
				{
					pms_id: "HUES-744381",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: "I-have",
					last_name: "Multiple",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: "15CV28143",
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
				{
					pms_id: "HUES-744382",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: "Rooms",
					last_name: "Testing",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: "58749865E",
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
			],
			check_in: "2022-12-15",
			check_out: "2022-12-18",
			res_date: null,
			res_room_number: "",
			res_room_type: "DH",
			res_board: "AL",
			booking_state: "reserved",
			stay_state: "scheduled",
			check_state: "none",
			check_state_date: "1900-01-01",
			res_adults: "2",
			res_children: null,
			res_juniors: null,
			res_babies: null,
			res_seniors: null,
			res_localizer: "MULTIPLE-TEST",
			res_id: "**********",
			res_nights: "3",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "EXPEDIA.COM",
			res_contract: null,
			res_comments: "",
			res_amount: 0,
			res_extras: "[]",
			res_currency: "EUR",
		},
		{
			brand_id: "1",
			guests: [
				{
					pms_id: "HUES-744383",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: "I-have",
					last_name: "Multiple",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: "15CV28143",
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
				{
					pms_id: "HUES-744384",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: "Rooms",
					last_name: "Testing",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: "58749865E",
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
			],
			check_in: "2022-12-15",
			check_out: "2022-12-18",
			res_date: null,
			res_room_number: "",
			res_room_type: "DH",
			res_board: "AL",
			booking_state: "reserved",
			stay_state: "scheduled",
			check_state: "none",
			check_state_date: "1900-01-01",
			res_adults: "2",
			res_children: null,
			res_juniors: null,
			res_babies: null,
			res_seniors: null,
			res_localizer: "MULTIPLE-TEST",
			res_id: "**********",
			res_nights: "3",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "EXPEDIA.COM",
			res_contract: null,
			res_comments: "",
			res_amount: 0,
			res_extras: "[]",
			res_currency: "EUR",
		},
	],
};

export const similarReservation = {
	data: [
		{
			brand_id: "1",
			guests: [
				{
					pms_id: "HUES-744381",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: "Pepe",
					last_name: "Viyuela",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: "85987451E",
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
				{
					pms_id: "HUES-744382",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: "Test",
					last_name: "Testing",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: "12345678A",
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
				{
					pms_id: null,
					position: null,
					validated: false,
					pax_type: "CH",
					first_name: null,
					last_name: null,
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: null,
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
			],
			check_in: "2022-02-14",
			check_out: "2022-02-15",
			res_date: null,
			res_room_number: "",
			res_room_type: "DH",
			res_board: "AL",
			booking_state: "reserved",
			stay_state: "scheduled",
			check_state: "none",
			check_state_date: "1900-01-01",
			res_adults: "1",
			res_children: "1",
			res_juniors: null,
			res_babies: null,
			res_seniors: null,
			res_localizer: "TESTLOCALIZER7",
			res_id: "**********",
			res_nights: "1",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "EXPEDIA.COM",
			res_contract: null,
			res_comments: "",
			res_amount: 0,
			res_extras: "[]",
			res_currency: "EUR",
		},
	],
};

export const allPaxReservation = {
	data: [
		{
			brand_id: "1",
			guests: [
				{
					pms_id: "HUES-744381",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: "Mokhles",
					last_name: "Pahlan",
					full_name: "Mokhles Pahlan",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: "15CV28143",
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
				{
					pms_id: "HUES-744382",
					position: null,
					validated: false,
					pax_type: "JR",
					first_name: "Test",
					last_name: "Testing",
					full_name: "Test Testing",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: "58749865E",
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
				{
					pms_id: null,
					position: null,
					validated: false,
					pax_type: "CH",
					first_name: "Child",
					last_name: "Childing",
					full_name: "Child Childing",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: null,
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
				{
					pms_id: null,
					position: null,
					validated: false,
					pax_type: "BB",
					first_name: "Baby",
					last_name: "Babying",
					full_name: "Baby Babying",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: null,
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
				{
					pms_id: null,
					position: null,
					validated: false,
					pax_type: null,
					first_name: "Null test",
					last_name: "Null test",
					full_name: "Null test Null test",
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: null,
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
			],
			check_in: "2022-12-12",
			check_out: "2022-12-13",
			res_date: null,
			res_room_number: "",
			res_room_type: "DH",
			res_board: "AL",
			booking_state: "reserved",
			stay_state: "scheduled",
			check_state: "none",
			check_state_date: "1900-01-01",
			res_adults: "1",
			res_children: "1",
			res_juniors: "1",
			res_babies: "1",
			res_seniors: null,
			res_localizer: "PAX-TEST",
			res_id: "**********",
			res_nights: "1",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "EXPEDIA.COM",
			res_contract: null,
			res_comments: "",
			res_amount: 0,
			res_extras: "[]",
			res_currency: "EUR",
		},
	],
};

export const notFoundReservation = {
	data: [],
};

export const finishedReservation = {
	data: [
		{
			brand_id: "1",
			guests: [
				{
					pms_id: "HUES-742422",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: null,
					last_name: null,
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: null,
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
				{
					pms_id: "HUES-742422",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: null,
					last_name: null,
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: null,
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
			],
			check_in: "2020-11-18",
			check_out: "2020-11-22",
			res_date: "2020-11-17",
			res_room_number: "",
			res_room_type: "DOBLE",
			res_board: "AD",
			booking_state: "none",
			stay_state: "scheduled",
			check_state: null,
			check_state_date: "2021-02-02",
			res_adults: "1",
			res_children: "1",
			res_juniors: null,
			res_babies: null,
			res_seniors: null,
			res_localizer: "02hotelinking",
			res_id: "**********",
			res_nights: "4",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "EXPEDIA.COM",
			res_contract: null,
			res_comments: "",
			res_amount: "168",
			res_extras: "[]",
			res_currency: "EUR",
		},
		{
			brand_id: "1",
			guests: [
				{
					pms_id: "HUES-743537",
					position: null,
					validated: true,
					pax_type: "AD",
					first_name: "Douglas",
					last_name: "Raiter",
					email: null,
					gender: "male",
					birthday: "1991-09-28",
					nationality: "USA",
					document_id: "*********",
					address: "Dasdadsa",
					city: "",
					province: null,
					postal_code: "dasdasda",
					telephone: "",
					birth_country: "USA",
					residence_country: "España",
				},
				{
					pms_id: "HUES-743538",
					position: null,
					validated: true,
					pax_type: "CH",
					first_name: "",
					last_name: "",
					email: null,
					gender: "",
					birthday: "2021-04-01",
					nationality: "ESP",
					document_id: "",
					address: "",
					city: "",
					province: null,
					postal_code: "",
					telephone: "",
					birth_country: "ESP",
					residence_country: "",
				},
			],
			check_in: "2021-05-01",
			check_out: "2021-05-21",
			res_date: null,
			res_room_number: "",
			res_room_type: "DH",
			res_board: "AL",
			booking_state: "reserved",
			stay_state: "scheduled",
			check_state: "none",
			check_state_date: "1900-01-01",
			res_adults: "1",
			res_children: "1",
			res_juniors: null,
			res_babies: null,
			res_seniors: null,
			res_localizer: "02hotelinking",
			res_id: "**********",
			res_nights: "20",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "EXPEDIA.COM",
			res_contract: null,
			res_comments: "",
			res_amount: 0,
			res_extras: "[]",
			res_currency: "EUR",
		},
	],
};

export const charges = {
	data: {
		localizer: "TESTLOCALIZER7",
		reservations: [
			{
				res_id: "**********",
				invoices: [
					{
						id: "PROF-000000",
						guest_id: "HUES-744531",
						name: "Daniel Alzina Daniel",
						total: 150,
						received: 0,
						remain: 150,
						taxes: 7.27,
						currency: "EUR",
						products: [
							{
								start_date: "2022-04-07T00:00:00+02:00",
								end_date: "2022-04-07T00:00:00+02:00",
								product_code: "0100",
								description: "ALOJAMIENTO",
								currency: "EUR",
								quantity: 1,
								total: 150,
								taxes: 7.27,
							},
						],
						transactions: [],
					},
				],
			},
			{
				res_id: "**********",
				invoices: [
					{
						id: "PROF-000001",
						guest_id: "HUES-744531",
						name: "Pepe viyuela",
						total: 300,
						received: 0,
						remain: 300,
						taxes: 7.27,
						currency: "EUR",
						products: [
							{
								start_date: "2022-04-07T00:00:00+02:00",
								end_date: "2022-04-07T00:00:00+02:00",
								product_code: "0100",
								description: "ALOJAMIENTO",
								currency: "EUR",
								quantity: 1,
								total: 300,
								taxes: 7.27,
							},
						],
						transactions: [],
					},
				],
			},
		],
	},
};

export const noCharges = {
	data: {
		localizer: "TESTLOCALIZER7",
		reservations: [
			{
				res_id: "**********",
				invoices: [],
			},
			{
				res_id: "**********",
				invoices: [],
			},
		],
	},
};

export const sanetizedCharges = {
	currency: "EUR",
	localizer: {
		amount: 450,
		selected: false,
	},
	reservations: [
		{
			id: "**********",
			invoices: [
				{
					amount: 150,
					currency: "EUR",
					guest_id: "HUES-744531",
					id: "PROF-000000",
					name: "Daniel Alzina Daniel",
					total: 150,
					received: 0,
					remain: 150,
					taxes: 7.27,
					products: [
						{
							start_date: "2022-04-07T00:00:00+02:00",
							end_date: "2022-04-07T00:00:00+02:00",
							product_code: "0100",
							description: "ALOJAMIENTO",
							currency: "EUR",
							quantity: 1,
							total: 150,
							taxes: 7.27,
						},
					],
					transactions: [],
				},
			],
			selected: false,
		},
		{
			id: "**********",
			invoices: [
				{
					amount: 300,
					id: "PROF-000001",
					guest_id: "HUES-744531",
					name: "Pepe viyuela",
					total: 300,
					received: 0,
					remain: 300,
					taxes: 7.27,
					currency: "EUR",
					products: [
						{
							start_date: "2022-04-07T00:00:00+02:00",
							end_date: "2022-04-07T00:00:00+02:00",
							product_code: "0100",
							description: "ALOJAMIENTO",
							currency: "EUR",
							quantity: 1,
							total: 300,
							taxes: 7.27,
						},
					],
					transactions: [],
				},
			],
			selected: false,
		},
	],
};

export const selectedReservation = {
	brand_id: "76",
	guests: [
		{
			pms_id: "HUES-744349",
			position: null,
			validated: true,
			pax_type: "AD",
			email: null,
			gender: "female",
			birthday: "2003-02-11",
			nationality: "ESP",
			document_id: "safdsadsa",
			address: "Carrer Miquel Forteza I Pinya Illes Balears",
			city: "",
			province: null,
			postal_code: "07007",
			telephone: "",
			birth_country: "ESP",
			residence_country: "España",
			processCompleted: true,
			name: "Test",
			surname: "Fsadsa",
			full_name: "Test Fsadsa",
		},
		{
			pms_id: "HUES-744365",
			position: null,
			validated: false,
			pax_type: "AD",
			first_name: null,
			last_name: null,
			email: null,
			gender: null,
			birthday: null,
			nationality: null,
			document_id: null,
			address: null,
			city: null,
			province: null,
			postal_code: null,
			telephone: null,
			birth_country: null,
			residence_country: null,
			processCompleted: false,
		},
		{
			pms_id: null,
			position: null,
			validated: false,
			pax_type: "CH",
			first_name: null,
			last_name: null,
			email: null,
			gender: null,
			birthday: null,
			nationality: null,
			document_id: null,
			address: null,
			city: null,
			province: null,
			postal_code: null,
			telephone: null,
			birth_country: null,
			residence_country: null,
			processCompleted: false,
		},
	],
	check_in: "2022-02-05",
	check_out: "2022-02-07",
	res_date: null,
	res_room_number: "521",
	res_room_type: "DH",
	res_board: "AL",
	booking_state: "reserved",
	stay_state: "scheduled",
	check_state: "pre_checked_in",
	check_state_date: "2022-02-07",
	res_adults: "1",
	res_children: "1",
	res_juniors: null,
	res_babies: null,
	res_seniors: null,
	res_localizer: "AAA321",
	res_id: "**********",
	res_nights: "2",
	res_agency: null,
	res_company: null,
	res_intermediary: null,
	res_channel: "EXPEDIA.COM",
	res_contract: null,
	res_comments: "",
	res_amount: 0,
	res_extras: "[]",
	res_currency: "EUR",
};
export const futureReservation = {
	data: [
		{
			brand_id: "76",
			guests: [
				{
					pms_id: "HUES-744341",
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: null,
					last_name: null,
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: null,
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
				{
					pms_id: null,
					position: null,
					validated: false,
					pax_type: "CH",
					first_name: null,
					last_name: null,
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: null,
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
				},
			],
			check_in: "2040-01-01",
			check_out: "2040-01-07",
			res_date: null,
			res_room_number: "",
			res_room_type: "DH",
			res_board: "AL",
			booking_state: "reserved",
			stay_state: "scheduled",
			check_state: "none",
			check_state_date: "1900-01-01",
			res_adults: "1",
			res_children: "1",
			res_juniors: null,
			res_babies: null,
			res_seniors: null,
			res_localizer: "TESTFUTURE",
			res_id: "**********",
			res_nights: "6",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "EXPEDIA.COM",
			res_contract: null,
			res_comments: "",
			res_amount: 0,
			res_extras: "[]",
			res_currency: "EUR",
		},
	],
};

export const redirectUrlResponse = {
	data: {
		url: ["https://hotelinking.com"],
	},
};

const daysToMiliseconds = (days) => {
	return days * 24 * 60 * 60 * 1000;
};

export const demoReservation = {
	data: [
		{
			brand_id: "1",
			guests: [
				{
					pms_id: null,
					position: null,
					validated: false,
					pax_type: "AD",
					first_name: null,
					last_name: null,
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: null,
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
					holder: false,
				},
				{
					pms_id: null,
					position: null,
					validated: false,
					pax_type: "CH",
					first_name: null,
					last_name: null,
					email: null,
					gender: null,
					birthday: null,
					nationality: null,
					document_id: null,
					address: null,
					city: null,
					province: null,
					postal_code: null,
					telephone: null,
					birth_country: null,
					residence_country: null,
					holder: false,
				}
			],
			check_in: format(Date.now() + daysToMiliseconds(3), "YYYY-MM-DD"),
			check_out: format(Date.now() + daysToMiliseconds(4), "YYYY-MM-DD"),
			res_date: null,
			res_room_number: "",
			res_room_type: "DH",
			res_board: "AL",
			booking_state: "reserved",
			stay_state: "scheduled",
			check_state: "none",
			check_state_date: "1900-01-01",
			res_adults: "2",
			res_children: "1",
			res_juniors: null,
			res_babies: null,
			res_seniors: null,
			res_localizer: "DEMO_RESERVATION",
			res_id: "**********",
			res_nights: "1",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "EXPEDIA.COM",
			res_contract: null,
			res_comments: "",
			res_amount: 0,
			res_extras: "[]",
			res_currency: "EUR",
		},
	],
};

export const demoCharges = {
	data: {
		localizer: "TESTLOCALIZER7",
		reservations: [
			{
				res_id: "**********",
				invoices: [
					{
						id: "PROF-000000",
						guest_id: "HUES-744531",
						name: "Daniel Alzina Daniel",
						total: 150,
						received: 0,
						remain: 150,
						taxes: 7.27,
						currency: "EUR",
						products: [
							{
								start_date: "2022-04-07T00:00:00+02:00",
								end_date: "2022-04-07T00:00:00+02:00",
								product_code: "0100",
								description: "ALOJAMIENTO",
								currency: "EUR",
								quantity: 1,
								total: 150,
								taxes: 7.27,
							},
						],
						transactions: [],
					},
				],
			},
		],
	},
};

export const multipleReservations = {
	data: [
		{
			brand_id: "76",
			guests: [
				{
					pms_id: null,
					pax_type: "AD",
					first_name: "Pepe",
					last_name: "Viyuela",
				},
			],
			check_in: "2023-02-16",
			check_out: "2023-02-21",
			res_date: null,
			res_room_number: "",
			res_room_type: "DH",
			res_board: "AL",
			booking_state: "reserved",
			stay_state: "scheduled",
			check_state: "none",
			check_state_date: "1900-01-01",
			res_adults: "1",
			res_children: "1",
			res_juniors: null,
			res_babies: null,
			res_seniors: null,
			res_localizer: "reserv8",
			res_id: "**********",
			res_nights: "20",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "EXPEDIA.COM",
			res_contract: null,
			res_comments: "",
			res_amount: 0,
			res_extras: "[]",
			res_currency: "EUR",
		},
		{
			brand_id: "76",
			guests: [
				{
					pms_id: null,
					pax_type: "AD",
					first_name: "Pepe",
					last_name: "Viyuela",
				},
			],
			check_in: "2023-02-16",
			check_out: "2023-02-21",
			res_date: null,
			res_room_number: "",
			res_room_type: "DH",
			res_board: "AL",
			booking_state: "reserved",
			stay_state: "scheduled",
			check_state: "none",
			check_state_date: "1900-01-01",
			res_adults: "1",
			res_children: "1",
			res_juniors: null,
			res_babies: null,
			res_seniors: null,
			res_localizer: "reserv8",
			res_id: "**********",
			res_nights: "20",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "EXPEDIA.COM",
			res_contract: null,
			res_comments: "",
			res_amount: 0,
			res_extras: "[]",
			res_currency: "EUR",
		},
		{
			brand_id: "76",
			guests: [
				{
					pms_id: null,
					pax_type: "AD",
					first_name: "Pepe",
					last_name: "Viyuela",
				},
			],
			check_in: "2023-02-16",
			check_out: "2023-02-21",
			res_date: null,
			res_room_number: "",
			res_room_type: "DH",
			res_board: "AL",
			booking_state: "reserved",
			stay_state: "scheduled",
			check_state: "none",
			check_state_date: "1900-01-01",
			res_adults: "1",
			res_children: "1",
			res_juniors: null,
			res_babies: null,
			res_seniors: null,
			res_localizer: "reserv9",
			res_id: "**********",
			res_nights: "20",
			res_agency: null,
			res_company: null,
			res_intermediary: null,
			res_channel: "EXPEDIA.COM",
			res_contract: null,
			res_comments: "",
			res_amount: 0,
			res_extras: "[]",
			res_currency: "EUR",
		},
	],
};

export const reservationLogicError = {
	error: {
		type: "LOGIC_ERROR",
		message: "Reservation is already on pre-checked-in-state",
		code: "INT_3_2",
	},
};

export const groupReservationError= {
	error: {
		code: "INT_3_10",
	},
}
